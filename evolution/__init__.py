"""
进化框架

用于进化代码生成和优化的综合框架。
提供种群管理、选择策略、变异操作、评估方法和数据持久化的模块化组件。

主要组件:
- Core: 代码表示和特征空间
- Organization: 种群和精英管理
- Evolution: 选择、变异和评估策略
- Data: 代码和资源的存储接口
- Controller: 进化过程的主要编排

使用示例:
    from evolution import EvolutionController, EvolutionConfig
    from evolution.data import MemoryCodeStorage

    config = EvolutionConfig(
        population={'num_islands': 1},
        elite={'max_size': 100},
        selection={'strategy': 'tournament'},
        variation={'operator': 'point_mutation'},
        evaluation={'evaluator': 'performance'}
    )

    storage = MemoryCodeStorage()
    controller = EvolutionController(config, storage)
    controller.initialize("print('Hello, World!')")

    for _ in range(100):
        stats = controller.step()
        print(f"Generation {stats['generation']}: {stats['new_codes']} new codes")
"""

# 基础版本号
__version__ = "1.0.0"

# 简化导入以避免循环依赖问题
# 用户应该直接从子模块导入具体组件

__all__ = [
    '__version__'
]
