"""
进化框架

用于进化代码生成和优化的综合框架。
提供种群管理、选择策略、变异操作、评估方法和数据持久化的模块化组件。

主要组件:
- Core: 代码表示和特征空间
- Organization: 种群和精英管理
- Evolution: 选择、变异和评估策略
- Data: 代码和资源的存储接口
- Controller: 进化过程的主要编排

使用示例:
    from evolution import EvolutionController, EvolutionConfig
    from evolution.data import MemoryCodeStorage

    config = EvolutionConfig(
        population={'num_islands': 1},
        elite={'max_size': 100},
        selection={'strategy': 'tournament'},
        variation={'operator': 'point_mutation'},
        evaluation={'evaluator': 'performance'}
    )

    storage = MemoryCodeStorage()
    controller = EvolutionController(config, storage)
    controller.initialize("print('Hello, World!')")

    for _ in range(100):
        stats = controller.step()
        print(f"Generation {stats['generation']}: {stats['new_codes']} new codes")
"""

# Core components
from .core import Code, FeatureSpace, FeatureSpaceConfig

# Organization components
from .organization import Population, PopulationConfig, Elite, EliteConfig

# Evolution components - import directly to avoid circular imports
# from .evolution.selection import Selection, create_selection
# from .evolution.variation import Variation, create_variation
# from .evolution.evaluation import Evaluator, create_evaluator

# Data components
from .data import CodeStorage, AssetStorage

# Main controller
from .controller import EvolutionController, EvolutionConfig

__version__ = "0.1.0"

__all__ = [
    # Core
    'Code',
    'FeatureSpace', 
    'FeatureSpaceConfig',
    
    # Organization
    'Population',
    'PopulationConfig', 
    'Elite',
    'EliteConfig',
    
    # Evolution - commented out to avoid circular imports
    # 'Selection',
    # 'create_selection',
    # 'Variation',
    # 'create_variation',
    # 'Evaluator',
    # 'create_evaluator',
    
    # Data
    'CodeStorage',
    'AssetStorage',
    
    # Controller
    'EvolutionController',
    'EvolutionConfig'
]
