"""
Evolution Framework

A comprehensive framework for evolutionary code generation and optimization.
Provides modular components for population management, selection strategies,
variation operators, evaluation methods, and data persistence.

Main Components:
- Core: Code representation and feature spaces
- Organization: Population and elite management  
- Evolution: Selection, variation, and evaluation strategies
- Data: Storage interfaces for codes and assets
- Controller: Main orchestration of the evolution process

Example Usage:
    from evolution import EvolutionController, EvolutionConfig
    from evolution.data import MemoryCodeStorage
    
    config = EvolutionConfig(
        population={'num_islands': 1},
        elite={'max_size': 100},
        selection={'strategy': 'tournament'},
        variation={'operator': 'point_mutation'},
        evaluation={'evaluator': 'performance'}
    )
    
    storage = MemoryCodeStorage()
    controller = EvolutionController(config, storage)
    controller.initialize("print('Hello, World!')")
    
    for _ in range(100):
        stats = controller.step()
        print(f"Generation {stats['generation']}: {stats['new_codes']} new codes")
"""

# Core components
from .core import Code, FeatureSpace, FeatureSpaceConfig

# Organization components
from .organization import Population, PopulationConfig, Elite, EliteConfig

# Evolution components - import directly to avoid circular imports
# from .evolution.selection import Selection, create_selection
# from .evolution.variation import Variation, create_variation
# from .evolution.evaluation import Evaluator, create_evaluator

# Data components
from .data import CodeStorage, AssetStorage

# Main controller
from .controller import EvolutionController, EvolutionConfig

__version__ = "0.1.0"

__all__ = [
    # Core
    'Code',
    'FeatureSpace', 
    'FeatureSpaceConfig',
    
    # Organization
    'Population',
    'PopulationConfig', 
    'Elite',
    'EliteConfig',
    
    # Evolution - commented out to avoid circular imports
    # 'Selection',
    # 'create_selection',
    # 'Variation',
    # 'create_variation',
    # 'Evaluator',
    # 'create_evaluator',
    
    # Data
    'CodeStorage',
    'AssetStorage',
    
    # Controller
    'EvolutionController',
    'EvolutionConfig'
]
