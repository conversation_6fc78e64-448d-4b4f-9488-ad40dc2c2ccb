#!/usr/bin/env python3
"""
批量翻译注释的脚本
"""

import os
import re

# 英文到中文的翻译映射
TRANSLATIONS = {
    # 通用词汇
    "Args:": "参数:",
    "Returns:": "返回:",
    "Raises:": "抛出:",
    "Attributes:": "属性:",
    "Parameters:": "参数:",
    "Note:": "注意:",
    "Example:": "示例:",
    "Examples:": "示例:",
    
    # 方法描述
    "Initialize": "初始化",
    "Validate": "验证",
    "Get": "获取",
    "Set": "设置",
    "Add": "添加",
    "Remove": "移除",
    "Delete": "删除",
    "Create": "创建",
    "Update": "更新",
    "Save": "保存",
    "Load": "加载",
    "Clear": "清空",
    "Check": "检查",
    "Find": "查找",
    "Search": "搜索",
    "Sample": "采样",
    "Select": "选择",
    "Apply": "应用",
    "Evaluate": "评估",
    "Compare": "比较",
    "Convert": "转换",
    "Perform": "执行",
    "Advance": "推进",
    
    # 常见短语
    "the configuration": "配置",
    "the code": "代码",
    "the population": "种群",
    "the elite": "精英",
    "the storage": "存储",
    "the asset": "资源",
    "the feature": "特征",
    "the space": "空间",
    "the controller": "控制器",
    "the evaluator": "评估器",
    "the selection": "选择",
    "the variation": "变异",
    "the mutation": "突变",
    "the crossover": "交叉",
    "the generation": "代数",
    "the island": "岛屿",
    "the migration": "迁移",
    "the metrics": "指标",
    "the statistics": "统计信息",
    "the instance": "实例",
    "the class": "类",
    "the method": "方法",
    "the function": "函数",
    "the parameter": "参数",
    "the argument": "参数",
    "the value": "值",
    "the result": "结果",
    "the data": "数据",
    "the file": "文件",
    "the path": "路径",
    "the directory": "目录",
    "the content": "内容",
    "the size": "大小",
    "the count": "数量",
    "the number": "数量",
    "the list": "列表",
    "the dictionary": "字典",
    "the string": "字符串",
    "the boolean": "布尔值",
    "the integer": "整数",
    "the float": "浮点数",
    
    # 条件和状态
    "if": "如果",
    "when": "当",
    "whether": "是否",
    "True if": "如果为True",
    "False if": "如果为False",
    "None if": "如果为None",
    "is invalid": "无效",
    "is valid": "有效",
    "is found": "找到",
    "is not found": "未找到",
    "is successful": "成功",
    "fails": "失败",
    "exists": "存在",
    "does not exist": "不存在",
    "available": "可用",
    "not available": "不可用",
    
    # 错误和异常
    "ValueError": "ValueError",
    "TypeError": "TypeError",
    "KeyError": "KeyError",
    "IndexError": "IndexError",
    "FileNotFoundError": "FileNotFoundError",
    "IOError": "IOError",
    "RuntimeError": "RuntimeError",
    "NotImplementedError": "NotImplementedError",
    "If configuration is invalid": "如果配置无效",
    "If operation fails": "如果操作失败",
    "If file not found": "如果文件未找到",
    "If parameters are invalid": "如果参数无效",
    
    # 数据类型
    "str": "str",
    "int": "int", 
    "float": "float",
    "bool": "bool",
    "list": "list",
    "dict": "dict",
    "tuple": "tuple",
    "set": "set",
    "Optional": "Optional",
    "Union": "Union",
    "List": "List",
    "Dict": "Dict",
    "Tuple": "Tuple",
    "Set": "Set",
    "Any": "Any",
}

def translate_comment_line(line):
    """翻译单行注释"""
    # 保持缩进
    indent = len(line) - len(line.lstrip())
    content = line.strip()
    
    if not content.startswith('"""') and not content.startswith('"'):
        return line
    
    # 应用翻译
    for en, zh in TRANSLATIONS.items():
        content = content.replace(en, zh)
    
    return ' ' * indent + content + '\n'

def translate_file_comments(filepath):
    """翻译文件中的注释"""
    print(f"翻译文件: {filepath}")
    
    with open(filepath, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    translated_lines = []
    for line in lines:
        translated_lines.append(translate_comment_line(line))
    
    with open(filepath, 'w', encoding='utf-8') as f:
        f.writelines(translated_lines)

def main():
    """主函数"""
    # 获取所有Python文件
    python_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.py') and file != 'translate_comments.py':
                python_files.append(os.path.join(root, file))
    
    print(f"找到 {len(python_files)} 个Python文件")
    
    for filepath in python_files:
        translate_file_comments(filepath)
    
    print("翻译完成!")

if __name__ == "__main__":
    main()
