[project]
name = "evolution"
version = "1.0.0"
description = "模块化的代码进化框架，提供完整的进化算法组件用于自动化代码生成、优化和改进"
readme = "README.md"
requires-python = ">=3.8"
authors = [
    {name = "Evolution Framework Team"}
]
keywords = ["evolution", "genetic-algorithm", "code-generation", "optimization", "ai"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Code Generators",
]
dependencies = []

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
]
storage = [
    "pymongo>=4.13.1",
]

[project.urls]
Homepage = "https://github.com/evolution-framework/evolution"
Documentation = "https://github.com/evolution-framework/evolution#readme"
Repository = "https://github.com/evolution-framework/evolution.git"
"Bug Tracker" = "https://github.com/evolution-framework/evolution/issues"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py38']

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
