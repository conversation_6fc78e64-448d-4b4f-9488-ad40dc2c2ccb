#!/usr/bin/env python3
"""
批量翻译剩余注释的脚本
"""

import os
import re

# 常见的英文到中文翻译映射
TRANSLATIONS = {
    # 文档字符串常用词
    "Args:": "参数:",
    "Arguments:": "参数:",
    "Parameters:": "参数:",
    "Returns:": "返回:",
    "Return:": "返回:",
    "Raises:": "抛出:",
    "Raise:": "抛出:",
    "Yields:": "产生:",
    "Yield:": "产生:",
    "Note:": "注意:",
    "Notes:": "注意:",
    "Example:": "示例:",
    "Examples:": "示例:",
    "See also:": "另见:",
    "Warning:": "警告:",
    "Warnings:": "警告:",
    
    # 常见动词
    "Initialize": "初始化",
    "Initializes": "初始化",
    "Create": "创建",
    "Creates": "创建",
    "Get": "获取",
    "Gets": "获取",
    "Set": "设置",
    "Sets": "设置",
    "Add": "添加",
    "Adds": "添加",
    "Remove": "移除",
    "Removes": "移除",
    "Delete": "删除",
    "Deletes": "删除",
    "Update": "更新",
    "Updates": "更新",
    "Save": "保存",
    "Saves": "保存",
    "Load": "加载",
    "Loads": "加载",
    "Clear": "清空",
    "Clears": "清空",
    "Check": "检查",
    "Checks": "检查",
    "Validate": "验证",
    "Validates": "验证",
    "Find": "查找",
    "Finds": "查找",
    "Search": "搜索",
    "Searches": "搜索",
    "Sample": "采样",
    "Samples": "采样",
    "Select": "选择",
    "Selects": "选择",
    "Apply": "应用",
    "Applies": "应用",
    "Evaluate": "评估",
    "Evaluates": "评估",
    "Compare": "比较",
    "Compares": "比较",
    "Convert": "转换",
    "Converts": "转换",
    "Perform": "执行",
    "Performs": "执行",
    "Execute": "执行",
    "Executes": "执行",
    "Run": "运行",
    "Runs": "运行",
    "Process": "处理",
    "Processes": "处理",
    
    # 常见名词
    "configuration": "配置",
    "config": "配置",
    "instance": "实例",
    "object": "对象",
    "class": "类",
    "method": "方法",
    "function": "函数",
    "parameter": "参数",
    "argument": "参数",
    "value": "值",
    "result": "结果",
    "output": "输出",
    "input": "输入",
    "data": "数据",
    "information": "信息",
    "content": "内容",
    "size": "大小",
    "count": "数量",
    "number": "数量",
    "amount": "数量",
    "list": "列表",
    "dictionary": "字典",
    "string": "字符串",
    "integer": "整数",
    "float": "浮点数",
    "boolean": "布尔值",
    "file": "文件",
    "path": "路径",
    "directory": "目录",
    "folder": "文件夹",
    "name": "名称",
    "identifier": "标识符",
    "code": "代码",
    "population": "种群",
    "elite": "精英",
    "selection": "选择",
    "variation": "变异",
    "mutation": "突变",
    "crossover": "交叉",
    "evaluation": "评估",
    "fitness": "适应度",
    "generation": "代数",
    "island": "岛屿",
    "migration": "迁移",
    "strategy": "策略",
    "algorithm": "算法",
    "framework": "框架",
    "system": "系统",
    "storage": "存储",
    "database": "数据库",
    "cache": "缓存",
    "memory": "内存",
    "disk": "磁盘",
    "network": "网络",
    "server": "服务器",
    "client": "客户端",
    "interface": "接口",
    "implementation": "实现",
    "abstract": "抽象",
    "concrete": "具体",
    "base": "基础",
    "derived": "派生",
    "parent": "父",
    "child": "子",
    "super": "超",
    "sub": "子",
    
    # 常见形容词
    "invalid": "无效",
    "valid": "有效",
    "empty": "空",
    "full": "满",
    "available": "可用",
    "unavailable": "不可用",
    "successful": "成功",
    "failed": "失败",
    "complete": "完成",
    "incomplete": "未完成",
    "finished": "完成",
    "unfinished": "未完成",
    "ready": "就绪",
    "busy": "忙碌",
    "active": "活跃",
    "inactive": "非活跃",
    "enabled": "启用",
    "disabled": "禁用",
    "required": "必需",
    "optional": "可选",
    "default": "默认",
    "custom": "自定义",
    "automatic": "自动",
    "manual": "手动",
    "public": "公共",
    "private": "私有",
    "protected": "受保护",
    "static": "静态",
    "dynamic": "动态",
    "local": "本地",
    "remote": "远程",
    "global": "全局",
    "temporary": "临时",
    "permanent": "永久",
    "current": "当前",
    "previous": "之前",
    "next": "下一个",
    "first": "第一个",
    "last": "最后一个",
    "initial": "初始",
    "final": "最终",
    "maximum": "最大",
    "minimum": "最小",
    "total": "总",
    "partial": "部分",
    "complete": "完整",
    
    # 常见短语
    "if successful": "如果成功",
    "if failed": "如果失败",
    "if found": "如果找到",
    "if not found": "如果未找到",
    "if exists": "如果存在",
    "if not exists": "如果不存在",
    "if available": "如果可用",
    "if not available": "如果不可用",
    "if valid": "如果有效",
    "if invalid": "如果无效",
    "if empty": "如果为空",
    "if not empty": "如果不为空",
    "True if": "如果为True",
    "False if": "如果为False",
    "None if": "如果为None",
    "otherwise": "否则",
    "by default": "默认情况下",
    "for example": "例如",
    "such as": "如",
    "and so on": "等等",
    "etc.": "等",
    
    # 错误信息
    "must be": "必须是",
    "should be": "应该是",
    "cannot be": "不能是",
    "must not be": "不能是",
    "at least": "至少",
    "at most": "最多",
    "greater than": "大于",
    "less than": "小于",
    "equal to": "等于",
    "not equal to": "不等于",
    "between": "在...之间",
    "within": "在...范围内",
    "outside": "在...范围外",
    
    # 异常类型保持英文
    "ValueError": "ValueError",
    "TypeError": "TypeError",
    "KeyError": "KeyError",
    "IndexError": "IndexError",
    "AttributeError": "AttributeError",
    "FileNotFoundError": "FileNotFoundError",
    "IOError": "IOError",
    "RuntimeError": "RuntimeError",
    "NotImplementedError": "NotImplementedError",
    "ImportError": "ImportError",
    "ModuleNotFoundError": "ModuleNotFoundError",
}

def translate_line(line):
    """翻译单行文本"""
    # 保持原始缩进
    indent = len(line) - len(line.lstrip())
    content = line.strip()
    
    # 跳过空行和非注释行
    if not content or not (content.startswith('"""') or content.startswith('"') or content.startswith('#')):
        return line
    
    # 应用翻译
    translated = content
    for en, zh in TRANSLATIONS.items():
        translated = translated.replace(en, zh)
    
    return ' ' * indent + translated + '\n'

def translate_file(filepath):
    """翻译文件中的注释"""
    print(f"翻译文件: {filepath}")
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        translated_lines = []
        for line in lines:
            translated_lines.append(translate_line(line))
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.writelines(translated_lines)
        
        print(f"✓ 完成: {filepath}")
    except Exception as e:
        print(f"✗ 错误: {filepath} - {e}")

def main():
    """主函数"""
    # 需要翻译的文件列表
    files_to_translate = [
        "evolution/selection/__init__.py",
        "evolution/variation/__init__.py", 
        "evolution/evaluation/__init__.py",
        "organization/elite.py",
        "controller.py",
        "data/code_storage.py",
        "data/asset_storage.py"
    ]
    
    print("开始批量翻译剩余注释...")
    
    for filepath in files_to_translate:
        if os.path.exists(filepath):
            translate_file(filepath)
        else:
            print(f"文件不存在: {filepath}")
    
    print("\n批量翻译完成!")

if __name__ == "__main__":
    main()
