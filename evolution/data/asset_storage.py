"""
资源存储接口和实现。

本模块提供存储和检索与代码实例相关的资源（如图像、模型、数据文件）的抽象接口。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union
import os
import time


class AssetStorage(ABC):
    """
    资源存储系统的抽象基类。

    提供在进化过程中存储和检索与代码实例相关的二进制资源。
    """
    
    @abstractmethod
    def save(self, code_id: str, name: str, 
            data: Union[bytes, str], 
            content_type: str) -> None:
        """
        保存与代码实例关联的资源。

        参数:
            code_id: 关联代码实例的ID
            name: 资源的名称/标识符
            data: 资源数据（字节或字符串）
            content_type: 资源的MIME类型

        抛出:
            AssetStorageError: 如果保存操作失败
        """
        pass
    
    @abstractmethod
    def get(self, code_id: str, name: str) -> Optional[bytes]:
        """
        通过代码ID和名称检索资源。

        参数:
            code_id: 关联代码实例的ID
            name: 资源的名称/标识符

        返回:
            如果找到则返回资源数据（字节），否则返回None

        抛出:
            AssetStorageError: 如果检索操作失败
        """
        pass
    
    @abstractmethod
    def list(self, code_id: str) -> List[Dict[str, str]]:
        """
        列出与代码实例关联的所有资源。

        参数:
            code_id: 代码实例的ID

        返回:
            包含资源元数据的字典列表:
            - 'name': 资源名称
            - 'content_type': MIME类型
            - 'size': 大小（字节）
            - 'created': 创建时间戳

        抛出:
            AssetStorageError: 如果列表操作失败
        """
        pass
    
    @abstractmethod
    def delete(self, code_id: str, name: str) -> bool:
        """
        删除资源。

        参数:
            code_id: 关联代码实例的ID
            name: 资源的名称/标识符

        返回:
            如果删除成功则返回True，如果资源未找到则返回False

        抛出:
            AssetStorageError: 如果删除操作失败
        """
        pass
    
    @abstractmethod
    def exists(self, code_id: str, name: str) -> bool:
        """
        检查资源是否存在。

        参数:
            code_id: 关联代码实例的ID
            name: 资源的名称/标识符

        返回:
            如果资源存在则返回True，否则返回False

        抛出:
            AssetStorageError: 如果检查操作失败
        """
        pass
    
    @abstractmethod
    def get_size(self, code_id: str, name: str) -> Optional[int]:
        """
        获取资源的大小（字节）。

        参数:
            code_id: 关联代码实例的ID
            name: 资源的名称/标识符

        返回:
            如果资源存在则返回大小（字节），否则返回None

        抛出:
            AssetStorageError: 如果大小操作失败
        """
        pass
    
    @abstractmethod
    def clear(self, code_id: Optional[str] = None) -> None:
        """
        从存储中移除资源。

        参数:
            code_id: 要清除资源的代码ID（None表示所有资源）

        抛出:
            AssetStorageError: 如果清除操作失败
        """
        pass
    
    @abstractmethod
    def get_stats(self) -> Dict[str, int]:
        """
        获取资源存储统计信息。

        返回:
            包含统计信息的字典，如:
            - 'total_assets': 存储的资源总数
            - 'total_size': 总存储大小（字节）
            - 'unique_codes': 拥有资源的代码数量

        抛出:
            AssetStorageError: 如果统计操作失败
        """
        pass


class AssetStorageError(Exception):
    """资源存储操作失败时抛出的异常。"""
    pass


class FileSystemAssetStorage(AssetStorage):
    """
    资源存储的文件系统实现。

    将资源作为文件存储在按代码ID组织的目录结构中。
    """
    
    def __init__(self, base_path: str):
        """
        初始化文件系统资源存储。

        参数:
            base_path: 存储资源的基础目录
        """
        self.base_path = base_path
        os.makedirs(base_path, exist_ok=True)
    
    def _get_asset_path(self, code_id: str, name: str) -> str:
        """获取资源文件的完整路径。"""
        code_dir = os.path.join(self.base_path, code_id)
        return os.path.join(code_dir, name)

    def _get_code_dir(self, code_id: str) -> str:
        """获取代码资源的目录路径。"""
        return os.path.join(self.base_path, code_id)
    
    def save(self, code_id: str, name: str, 
            data: Union[bytes, str], 
            content_type: str) -> None:
        """将资源保存到文件系统。"""
        try:
            code_dir = self._get_code_dir(code_id)
            os.makedirs(code_dir, exist_ok=True)
            
            asset_path = self._get_asset_path(code_id, name)
            
            # 如果需要，将字符串转换为字节
            if isinstance(data, str):
                data = data.encode('utf-8')

            with open(asset_path, 'wb') as f:
                f.write(data)

            # 保存元数据
            metadata_path = asset_path + '.meta'
            metadata = f"{content_type}\n{len(data)}\n{time.time()}"
            with open(metadata_path, 'w') as f:
                f.write(metadata)
                
        except Exception as e:
            raise AssetStorageError(f"保存资源失败: {e}")

    def get(self, code_id: str, name: str) -> Optional[bytes]:
        """从文件系统获取资源。"""
        try:
            asset_path = self._get_asset_path(code_id, name)
            if not os.path.exists(asset_path):
                return None
                
            with open(asset_path, 'rb') as f:
                return f.read()
                
        except Exception as e:
            raise AssetStorageError(f"Failed to get asset: {e}")
    
    def list(self, code_id: str) -> List[Dict[str, str]]:
        """List assets for a code from file system."""
        try:
            code_dir = self._get_code_dir(code_id)
            if not os.path.exists(code_dir):
                return []
            
            assets = []
            for filename in os.listdir(code_dir):
                if filename.endswith('.meta'):
                    continue
                    
                asset_path = os.path.join(code_dir, filename)
                metadata_path = asset_path + '.meta'
                
                # Get metadata if available
                content_type = "application/octet-stream"
                size = os.path.getsize(asset_path)
                created = os.path.getctime(asset_path)
                
                if os.path.exists(metadata_path):
                    with open(metadata_path, 'r') as f:
                        lines = f.read().strip().split('\n')
                        if len(lines) >= 3:
                            content_type = lines[0]
                            size = int(lines[1])
                            created = float(lines[2])
                
                assets.append({
                    'name': filename,
                    'content_type': content_type,
                    'size': str(size),
                    'created': str(created)
                })
            
            return assets
            
        except Exception as e:
            raise AssetStorageError(f"Failed to list assets: {e}")
    
    def delete(self, code_id: str, name: str) -> bool:
        """Delete asset from file system."""
        try:
            asset_path = self._get_asset_path(code_id, name)
            metadata_path = asset_path + '.meta'
            
            deleted = False
            if os.path.exists(asset_path):
                os.remove(asset_path)
                deleted = True
                
            if os.path.exists(metadata_path):
                os.remove(metadata_path)
            
            return deleted
            
        except Exception as e:
            raise AssetStorageError(f"Failed to delete asset: {e}")
    
    def exists(self, code_id: str, name: str) -> bool:
        """Check if asset exists in file system."""
        asset_path = self._get_asset_path(code_id, name)
        return os.path.exists(asset_path)
    
    def get_size(self, code_id: str, name: str) -> Optional[int]:
        """Get asset size from file system."""
        try:
            asset_path = self._get_asset_path(code_id, name)
            if not os.path.exists(asset_path):
                return None
            return os.path.getsize(asset_path)
        except Exception as e:
            raise AssetStorageError(f"Failed to get asset size: {e}")
    
    def clear(self, code_id: Optional[str] = None) -> None:
        """Clear assets from file system."""
        try:
            if code_id is None:
                # Clear all assets
                import shutil
                if os.path.exists(self.base_path):
                    shutil.rmtree(self.base_path)
                os.makedirs(self.base_path, exist_ok=True)
            else:
                # Clear assets for specific code
                code_dir = self._get_code_dir(code_id)
                if os.path.exists(code_dir):
                    import shutil
                    shutil.rmtree(code_dir)
        except Exception as e:
            raise AssetStorageError(f"Failed to clear assets: {e}")
    
    def get_stats(self) -> Dict[str, int]:
        """Get file system asset storage statistics."""
        try:
            total_assets = 0
            total_size = 0
            unique_codes = 0
            
            if os.path.exists(self.base_path):
                for code_id in os.listdir(self.base_path):
                    code_dir = os.path.join(self.base_path, code_id)
                    if os.path.isdir(code_dir):
                        unique_codes += 1
                        for filename in os.listdir(code_dir):
                            if not filename.endswith('.meta'):
                                total_assets += 1
                                asset_path = os.path.join(code_dir, filename)
                                total_size += os.path.getsize(asset_path)
            
            return {
                'total_assets': total_assets,
                'total_size': total_size,
                'unique_codes': unique_codes
            }
        except Exception as e:
            raise AssetStorageError(f"Failed to get stats: {e}")
