"""
资源存储接口和实现。

本模块提供存储和检索与代码实例相关的资源（如图像、模型、数据文件）的抽象接口。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union
import os
import time


class AssetStorage(ABC):
    """
    资源存储系统的抽象基类。

    提供在进化过程中存储和检索与代码实例相关的二进制资源。
    """
    
    @abstractmethod
    def save(self, code_id: str, name: str, 
            data: Union[bytes, str], 
            content_type: str) -> None:
        """
        Save an asset associated with a code instance.
        
        Args:
            code_id: ID of the associated code instance
            name: Name/identifier for the asset
            data: Asset data (bytes or string)
            content_type: MIME type of the asset
            
        Raises:
            AssetStorageError: If save operation fails
        """
        pass
    
    @abstractmethod
    def get(self, code_id: str, name: str) -> Optional[bytes]:
        """
        Retrieve an asset by code ID and name.
        
        Args:
            code_id: ID of the associated code instance
            name: Name/identifier of the asset
            
        Returns:
            Asset data as bytes if found, None otherwise
            
        Raises:
            AssetStorageError: If retrieval operation fails
        """
        pass
    
    @abstractmethod
    def list(self, code_id: str) -> List[Dict[str, str]]:
        """
        List all assets associated with a code instance.
        
        Args:
            code_id: ID of the code instance
            
        Returns:
            List of dictionaries containing asset metadata:
            - 'name': asset name
            - 'content_type': MIME type
            - 'size': size in bytes
            - 'created': creation timestamp
            
        Raises:
            AssetStorageError: If list operation fails
        """
        pass
    
    @abstractmethod
    def delete(self, code_id: str, name: str) -> bool:
        """
        Delete an asset.
        
        Args:
            code_id: ID of the associated code instance
            name: Name/identifier of the asset
            
        Returns:
            True if deletion was successful, False if asset not found
            
        Raises:
            AssetStorageError: If delete operation fails
        """
        pass
    
    @abstractmethod
    def exists(self, code_id: str, name: str) -> bool:
        """
        Check if an asset exists.
        
        Args:
            code_id: ID of the associated code instance
            name: Name/identifier of the asset
            
        Returns:
            True if asset exists, False otherwise
            
        Raises:
            AssetStorageError: If check operation fails
        """
        pass
    
    @abstractmethod
    def get_size(self, code_id: str, name: str) -> Optional[int]:
        """
        Get the size of an asset in bytes.
        
        Args:
            code_id: ID of the associated code instance
            name: Name/identifier of the asset
            
        Returns:
            Size in bytes if asset exists, None otherwise
            
        Raises:
            AssetStorageError: If size operation fails
        """
        pass
    
    @abstractmethod
    def clear(self, code_id: Optional[str] = None) -> None:
        """
        Remove assets from storage.
        
        Args:
            code_id: ID of code to clear assets for (None for all assets)
            
        Raises:
            AssetStorageError: If clear operation fails
        """
        pass
    
    @abstractmethod
    def get_stats(self) -> Dict[str, int]:
        """
        Get asset storage statistics.
        
        Returns:
            Dictionary containing statistics like:
            - 'total_assets': total number of stored assets
            - 'total_size': total storage size in bytes
            - 'unique_codes': number of codes with assets
            
        Raises:
            AssetStorageError: If stats operation fails
        """
        pass


class AssetStorageError(Exception):
    """Exception raised when asset storage operations fail."""
    pass


class FileSystemAssetStorage(AssetStorage):
    """
    File system implementation of asset storage.
    
    Stores assets as files in a directory structure organized by code ID.
    """
    
    def __init__(self, base_path: str):
        """
        Initialize file system asset storage.
        
        Args:
            base_path: Base directory for storing assets
        """
        self.base_path = base_path
        os.makedirs(base_path, exist_ok=True)
    
    def _get_asset_path(self, code_id: str, name: str) -> str:
        """Get full path for an asset file."""
        code_dir = os.path.join(self.base_path, code_id)
        return os.path.join(code_dir, name)
    
    def _get_code_dir(self, code_id: str) -> str:
        """Get directory path for a code's assets."""
        return os.path.join(self.base_path, code_id)
    
    def save(self, code_id: str, name: str, 
            data: Union[bytes, str], 
            content_type: str) -> None:
        """Save asset to file system."""
        try:
            code_dir = self._get_code_dir(code_id)
            os.makedirs(code_dir, exist_ok=True)
            
            asset_path = self._get_asset_path(code_id, name)
            
            # Convert string to bytes if necessary
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            with open(asset_path, 'wb') as f:
                f.write(data)
                
            # Save metadata
            metadata_path = asset_path + '.meta'
            metadata = f"{content_type}\n{len(data)}\n{time.time()}"
            with open(metadata_path, 'w') as f:
                f.write(metadata)
                
        except Exception as e:
            raise AssetStorageError(f"Failed to save asset: {e}")
    
    def get(self, code_id: str, name: str) -> Optional[bytes]:
        """Get asset from file system."""
        try:
            asset_path = self._get_asset_path(code_id, name)
            if not os.path.exists(asset_path):
                return None
                
            with open(asset_path, 'rb') as f:
                return f.read()
                
        except Exception as e:
            raise AssetStorageError(f"Failed to get asset: {e}")
    
    def list(self, code_id: str) -> List[Dict[str, str]]:
        """List assets for a code from file system."""
        try:
            code_dir = self._get_code_dir(code_id)
            if not os.path.exists(code_dir):
                return []
            
            assets = []
            for filename in os.listdir(code_dir):
                if filename.endswith('.meta'):
                    continue
                    
                asset_path = os.path.join(code_dir, filename)
                metadata_path = asset_path + '.meta'
                
                # Get metadata if available
                content_type = "application/octet-stream"
                size = os.path.getsize(asset_path)
                created = os.path.getctime(asset_path)
                
                if os.path.exists(metadata_path):
                    with open(metadata_path, 'r') as f:
                        lines = f.read().strip().split('\n')
                        if len(lines) >= 3:
                            content_type = lines[0]
                            size = int(lines[1])
                            created = float(lines[2])
                
                assets.append({
                    'name': filename,
                    'content_type': content_type,
                    'size': str(size),
                    'created': str(created)
                })
            
            return assets
            
        except Exception as e:
            raise AssetStorageError(f"Failed to list assets: {e}")
    
    def delete(self, code_id: str, name: str) -> bool:
        """Delete asset from file system."""
        try:
            asset_path = self._get_asset_path(code_id, name)
            metadata_path = asset_path + '.meta'
            
            deleted = False
            if os.path.exists(asset_path):
                os.remove(asset_path)
                deleted = True
                
            if os.path.exists(metadata_path):
                os.remove(metadata_path)
            
            return deleted
            
        except Exception as e:
            raise AssetStorageError(f"Failed to delete asset: {e}")
    
    def exists(self, code_id: str, name: str) -> bool:
        """Check if asset exists in file system."""
        asset_path = self._get_asset_path(code_id, name)
        return os.path.exists(asset_path)
    
    def get_size(self, code_id: str, name: str) -> Optional[int]:
        """Get asset size from file system."""
        try:
            asset_path = self._get_asset_path(code_id, name)
            if not os.path.exists(asset_path):
                return None
            return os.path.getsize(asset_path)
        except Exception as e:
            raise AssetStorageError(f"Failed to get asset size: {e}")
    
    def clear(self, code_id: Optional[str] = None) -> None:
        """Clear assets from file system."""
        try:
            if code_id is None:
                # Clear all assets
                import shutil
                if os.path.exists(self.base_path):
                    shutil.rmtree(self.base_path)
                os.makedirs(self.base_path, exist_ok=True)
            else:
                # Clear assets for specific code
                code_dir = self._get_code_dir(code_id)
                if os.path.exists(code_dir):
                    import shutil
                    shutil.rmtree(code_dir)
        except Exception as e:
            raise AssetStorageError(f"Failed to clear assets: {e}")
    
    def get_stats(self) -> Dict[str, int]:
        """Get file system asset storage statistics."""
        try:
            total_assets = 0
            total_size = 0
            unique_codes = 0
            
            if os.path.exists(self.base_path):
                for code_id in os.listdir(self.base_path):
                    code_dir = os.path.join(self.base_path, code_id)
                    if os.path.isdir(code_dir):
                        unique_codes += 1
                        for filename in os.listdir(code_dir):
                            if not filename.endswith('.meta'):
                                total_assets += 1
                                asset_path = os.path.join(code_dir, filename)
                                total_size += os.path.getsize(asset_path)
            
            return {
                'total_assets': total_assets,
                'total_size': total_size,
                'unique_codes': unique_codes
            }
        except Exception as e:
            raise AssetStorageError(f"Failed to get stats: {e}")
