"""
代码存储接口和实现。

本模块提供在进化过程中持久化和检索代码实例的抽象接口。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any

from ..core.code import Code


class CodeStorage(ABC):
    """
    代码存储系统的抽象基类。

    提供代码实例的持久化存储和检索，支持查询和索引。
    """
    
    @abstractmethod
    def save(self, code: Code) -> None:
        """
        将代码实例保存到存储。

        参数:
            code: 要保存的代码实例

        抛出:
            StorageError: 如果保存操作失败
        """
        pass
    
    @abstractmethod
    def get(self, code_id: str) -> Optional[Code]:
        """
        通过ID检索代码实例。

        参数:
            code_id: 代码的唯一标识符

        返回:
            如果找到则返回代码实例，否则返回None

        抛出:
            StorageError: 如果检索操作失败
        """
        pass
    
    @abstractmethod
    def find(self, query: Dict[str, Any], 
            limit: int = 10, 
            sort_by: Optional[str] = None,
            ascending: bool = True) -> List[Code]:
        """
        查找匹配查询条件的代码。

        参数:
            query: 查询条件（字段:值对）
            limit: 返回结果的最大数量
            sort_by: 排序字段
            ascending: 排序顺序（True为升序，False为降序）

        返回:
            匹配的代码实例列表

        抛出:
            StorageError: 如果查询操作失败
        """
        pass
    
    @abstractmethod
    def update(self, code: Code) -> bool:
        """
        Update an existing code instance.
        
        Args:
            code: Code instance with updated data
            
        Returns:
            True if update was successful, False if code not found
            
        Raises:
            StorageError: If update operation fails
        """
        pass
    
    @abstractmethod
    def delete(self, code_id: str) -> bool:
        """
        Delete a code instance from storage.
        
        Args:
            code_id: Unique identifier of the code to delete
            
        Returns:
            True if deletion was successful, False if code not found
            
        Raises:
            StorageError: If delete operation fails
        """
        pass
    
    @abstractmethod
    def exists(self, code_id: str) -> bool:
        """
        Check if a code instance exists in storage.
        
        Args:
            code_id: Unique identifier of the code
            
        Returns:
            True if code exists, False otherwise
            
        Raises:
            StorageError: If check operation fails
        """
        pass
    
    @abstractmethod
    def count(self, query: Optional[Dict[str, Any]] = None) -> int:
        """
        Count codes matching query criteria.
        
        Args:
            query: Query criteria (None for total count)
            
        Returns:
            Number of matching codes
            
        Raises:
            StorageError: If count operation fails
        """
        pass
    
    @abstractmethod
    def clear(self) -> None:
        """
        Remove all codes from storage.
        
        Raises:
            StorageError: If clear operation fails
        """
        pass
    
    @abstractmethod
    def get_stats(self) -> Dict[str, Any]:
        """
        Get storage statistics.
        
        Returns:
            Dictionary containing storage statistics like:
            - 'total_codes': total number of stored codes
            - 'storage_size': storage size in bytes
            - 'last_updated': timestamp of last update
            
        Raises:
            StorageError: If stats operation fails
        """
        pass
    
    @abstractmethod
    def backup(self, path: str) -> None:
        """
        Create a backup of the storage.
        
        Args:
            path: Path where backup should be created
            
        Raises:
            StorageError: If backup operation fails
        """
        pass
    
    @abstractmethod
    def restore(self, path: str) -> None:
        """
        Restore storage from a backup.
        
        Args:
            path: Path to backup file
            
        Raises:
            StorageError: If restore operation fails
        """
        pass


class StorageError(Exception):
    """存储操作失败时抛出的异常。"""
    pass


class MemoryCodeStorage(CodeStorage):
    """
    代码存储的内存实现。

    使用字典在内存中存储代码。适用于测试和小规模实验，但不会在会话间持久化。
    """
    
    def __init__(self):
        """初始化空的内存存储。"""
        self._codes: Dict[str, Code] = {}

    def save(self, code: Code) -> None:
        """将代码保存到内存。"""
        self._codes[code.id] = code

    def get(self, code_id: str) -> Optional[Code]:
        """从内存获取代码。"""
        return self._codes.get(code_id)

    def find(self, query: Dict[str, Any],
            limit: int = 10,
            sort_by: Optional[str] = None,
            ascending: bool = True) -> List[Code]:
        """在内存中查找匹配查询的代码。"""
        # 具体实现将在具体实现中提供
        raise NotImplementedError("需要实现")

    def update(self, code: Code) -> bool:
        """在内存中更新代码。"""
        if code.id in self._codes:
            self._codes[code.id] = code
            return True
        return False

    def delete(self, code_id: str) -> bool:
        """从内存中删除代码。"""
        if code_id in self._codes:
            del self._codes[code_id]
            return True
        return False

    def exists(self, code_id: str) -> bool:
        """检查代码是否存在于内存中。"""
        return code_id in self._codes

    def count(self, query: Optional[Dict[str, Any]] = None) -> int:
        """计算内存中的代码数量。"""
        if query is None:
            return len(self._codes)
        # 实现将基于查询进行过滤
        raise NotImplementedError("需要查询过滤实现")

    def clear(self) -> None:
        """清空内存中的所有代码。"""
        self._codes.clear()

    def get_stats(self) -> Dict[str, Any]:
        """获取内存存储统计信息。"""
        return {
            'total_codes': len(self._codes),
            'storage_size': sum(len(str(code.to_dict())) for code in self._codes.values()),
            'last_updated': max((code.timestamp for code in self._codes.values()), default=0)
        }

    def backup(self, path: str) -> None:
        """创建内存存储的备份。"""
        # 实现将序列化代码到文件
        raise NotImplementedError("需要实现")

    def restore(self, path: str) -> None:
        """从备份恢复内存存储。"""
        # 实现将从文件反序列化代码
        raise NotImplementedError("需要实现")
