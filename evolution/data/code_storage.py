"""
代码存储接口和实现。

本模块提供在进化过程中持久化和检索代码实例的抽象接口。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any

from ..core.code import Code


class CodeStorage(ABC):
    """
    代码存储系统的抽象基类。

    提供代码实例的持久化存储和检索，支持查询和索引。
    """
    
    @abstractmethod
    def save(self, code: Code) -> None:
        """
        Save a code instance to storage.
        
        Args:
            code: Code instance to save
            
        Raises:
            StorageError: If save operation fails
        """
        pass
    
    @abstractmethod
    def get(self, code_id: str) -> Optional[Code]:
        """
        Retrieve a code instance by ID.
        
        Args:
            code_id: Unique identifier of the code
            
        Returns:
            Code instance if found, None otherwise
            
        Raises:
            StorageError: If retrieval operation fails
        """
        pass
    
    @abstractmethod
    def find(self, query: Dict[str, Any], 
            limit: int = 10, 
            sort_by: Optional[str] = None,
            ascending: bool = True) -> List[Code]:
        """
        Find codes matching query criteria.
        
        Args:
            query: Query criteria (field: value pairs)
            limit: Maximum number of results to return
            sort_by: Field to sort results by
            ascending: Sort order (True for ascending, False for descending)
            
        Returns:
            List of matching code instances
            
        Raises:
            StorageError: If query operation fails
        """
        pass
    
    @abstractmethod
    def update(self, code: Code) -> bool:
        """
        Update an existing code instance.
        
        Args:
            code: Code instance with updated data
            
        Returns:
            True if update was successful, False if code not found
            
        Raises:
            StorageError: If update operation fails
        """
        pass
    
    @abstractmethod
    def delete(self, code_id: str) -> bool:
        """
        Delete a code instance from storage.
        
        Args:
            code_id: Unique identifier of the code to delete
            
        Returns:
            True if deletion was successful, False if code not found
            
        Raises:
            StorageError: If delete operation fails
        """
        pass
    
    @abstractmethod
    def exists(self, code_id: str) -> bool:
        """
        Check if a code instance exists in storage.
        
        Args:
            code_id: Unique identifier of the code
            
        Returns:
            True if code exists, False otherwise
            
        Raises:
            StorageError: If check operation fails
        """
        pass
    
    @abstractmethod
    def count(self, query: Optional[Dict[str, Any]] = None) -> int:
        """
        Count codes matching query criteria.
        
        Args:
            query: Query criteria (None for total count)
            
        Returns:
            Number of matching codes
            
        Raises:
            StorageError: If count operation fails
        """
        pass
    
    @abstractmethod
    def clear(self) -> None:
        """
        Remove all codes from storage.
        
        Raises:
            StorageError: If clear operation fails
        """
        pass
    
    @abstractmethod
    def get_stats(self) -> Dict[str, Any]:
        """
        Get storage statistics.
        
        Returns:
            Dictionary containing storage statistics like:
            - 'total_codes': total number of stored codes
            - 'storage_size': storage size in bytes
            - 'last_updated': timestamp of last update
            
        Raises:
            StorageError: If stats operation fails
        """
        pass
    
    @abstractmethod
    def backup(self, path: str) -> None:
        """
        Create a backup of the storage.
        
        Args:
            path: Path where backup should be created
            
        Raises:
            StorageError: If backup operation fails
        """
        pass
    
    @abstractmethod
    def restore(self, path: str) -> None:
        """
        Restore storage from a backup.
        
        Args:
            path: Path to backup file
            
        Raises:
            StorageError: If restore operation fails
        """
        pass


class StorageError(Exception):
    """Exception raised when storage operations fail."""
    pass


class MemoryCodeStorage(CodeStorage):
    """
    In-memory implementation of code storage.
    
    Stores codes in memory using dictionaries. Suitable for testing
    and small-scale experiments but not persistent across sessions.
    """
    
    def __init__(self):
        """Initialize empty in-memory storage."""
        self._codes: Dict[str, Code] = {}
    
    def save(self, code: Code) -> None:
        """Save code to memory."""
        self._codes[code.id] = code
    
    def get(self, code_id: str) -> Optional[Code]:
        """Get code from memory."""
        return self._codes.get(code_id)
    
    def find(self, query: Dict[str, Any], 
            limit: int = 10, 
            sort_by: Optional[str] = None,
            ascending: bool = True) -> List[Code]:
        """Find codes in memory matching query."""
        # Implementation would be provided in concrete implementation
        raise NotImplementedError("Implementation required")
    
    def update(self, code: Code) -> bool:
        """Update code in memory."""
        if code.id in self._codes:
            self._codes[code.id] = code
            return True
        return False
    
    def delete(self, code_id: str) -> bool:
        """Delete code from memory."""
        if code_id in self._codes:
            del self._codes[code_id]
            return True
        return False
    
    def exists(self, code_id: str) -> bool:
        """Check if code exists in memory."""
        return code_id in self._codes
    
    def count(self, query: Optional[Dict[str, Any]] = None) -> int:
        """Count codes in memory."""
        if query is None:
            return len(self._codes)
        # Implementation would filter based on query
        raise NotImplementedError("Query filtering implementation required")
    
    def clear(self) -> None:
        """Clear all codes from memory."""
        self._codes.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get memory storage statistics."""
        return {
            'total_codes': len(self._codes),
            'storage_size': sum(len(str(code.to_dict())) for code in self._codes.values()),
            'last_updated': max((code.timestamp for code in self._codes.values()), default=0)
        }
    
    def backup(self, path: str) -> None:
        """Create backup of memory storage."""
        # Implementation would serialize codes to file
        raise NotImplementedError("Implementation required")
    
    def restore(self, path: str) -> None:
        """Restore memory storage from backup."""
        # Implementation would deserialize codes from file
        raise NotImplementedError("Implementation required")
