# Evolution Framework - 代码进化框架

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Documentation](https://img.shields.io/badge/Documentation-中文-red.svg)](#文档)

Evolution是一个模块化的代码进化框架，提供了完整的进化算法组件，用于自动化代码生成、优化和改进。框架采用现代软件工程原则设计，具有高度的可扩展性和可维护性。

## ✨ 特性

- 🧬 **完整的进化算法组件**: 选择、变异、评估、精英管理
- 🏗️ **模块化架构**: 基于SOLID原则的清晰分层设计
- 🌐 **中文文档**: 完整的中文API文档和注释
- 🔧 **高度可扩展**: 支持插件式扩展新的算法和策略
- 💾 **灵活存储**: 支持多种数据存储方式
- 🏝️ **岛屿模型**: 支持分布式进化和种群迁移
- 📊 **丰富统计**: 详细的进化过程统计和监控

## 🚀 快速开始

### 安装

```bash
# 克隆仓库
git clone <repository-url>
cd evolution

# 安装依赖
pip install -r requirements.txt
```

### 基本使用

```python
from evolution import Code, EvolutionController, EvolutionConfig
from evolution.data.code_storage import MemoryCodeStorage

# 创建代码实例
code = Code(
    content="def hello(): return 'Hello, World!'",
    metrics={"performance": 0.95},
    features={"complexity": 0.2}
)

# 创建配置
config = EvolutionConfig(
    population={"num_islands": 2, "max_size": 100},
    elite={"max_size": 50, "min_improvement": 0.01},
    selection={"strategy": "tournament", "tournament_size": 3},
    variation={"operator": "point_mutation", "mutation_rate": 0.1},
    evaluation={"evaluator": "performance", "timeout": 10.0}
)

# 创建存储
storage = MemoryCodeStorage()

# 创建控制器
controller = EvolutionController(config, storage)

# 初始化并运行
controller.initialize("def initial(): pass")
for generation in range(100):
    stats = controller.step()
    print(f"Generation {generation}: {stats}")

    # 获取最佳代码
    best = controller.get_best()
    if best:
        print(f"Best code: {best.content}")
```

## 📁 项目结构

```
evolution/
├── __init__.py                    # 主包入口
├── controller.py                  # 进化过程主控制器
├── core/                         # 核心数据结构
│   ├── __init__.py
│   ├── code.py                   # Code类 - 代码表示
│   └── feature.py                # FeatureSpace - 特征空间管理
├── organization/                 # 代码组织方式
│   ├── __init__.py
│   ├── population.py             # Population - 种群管理
│   └── elite.py                  # Elite - 精英管理
├── evolution/                    # 进化算法实现
│   ├── __init__.py
│   ├── selection/
│   │   └── __init__.py           # Selection - 选择策略
│   ├── variation/
│   │   └── __init__.py           # Variation - 变异操作
│   └── evaluation/
│       └── __init__.py           # Evaluator - 评估策略
├── data/                         # 数据持久化
│   ├── __init__.py
│   ├── code_storage.py           # CodeStorage - 代码存储
│   └── asset_storage.py          # AssetStorage - 资源存储
└── README.md                     # 项目文档
```

## 🧩 核心组件

### 代码表示 (Code)
```python
from evolution.core.code import Code

code = Code(
    content="def fibonacci(n): return n if n <= 1 else fibonacci(n-1) + fibonacci(n-2)",
    metrics={"performance": 0.8, "readability": 0.9},
    features={"complexity": 0.6, "recursion": 1.0},
    generation=5
)
```

### 种群管理 (Population)
```python
from evolution.organization.population import Population

# 支持岛屿模型的种群管理
population = Population(config={
    "num_islands": 4,
    "max_size": 200,
    "migration_interval": 10,
    "migration_rate": 0.1
})
```

### 精英管理 (Elite)
```python
from evolution.organization.elite import Elite

# 自动管理最优代码
elite = Elite(config={
    "max_size": 50,
    "min_improvement": 0.01
})
```

### 选择策略
```python
from evolution.evolution.selection import create_selection

# 锦标赛选择
selection = create_selection("tournament", tournament_size=3, metric="fitness")

# 轮盘赌选择
selection = create_selection("roulette_wheel", metric="performance")

# 排名选择
selection = create_selection("rank", pressure=1.5)
```

### 变异操作
```python
from evolution.evolution.variation import create_variation

# 点突变
mutation = create_variation("point_mutation", mutation_rate=0.1, mutation_strength=0.1)

# 均匀交叉
crossover = create_variation("uniform_crossover", crossover_rate=0.5)

# 语义突变
semantic = create_variation("semantic_mutation", semantic_strength=0.2)
```

### 评估策略
```python
from evolution.evolution.evaluation import create_evaluator

# 性能评估
evaluator = create_evaluator("performance", timeout=10.0, memory_limit=1024*1024)

# 质量评估
evaluator = create_evaluator("quality", metrics=["complexity", "readability"])

# 组合评估
evaluator = create_evaluator("composite",
    evaluators={"perf": perf_eval, "quality": quality_eval},
    weights={"perf": 0.7, "quality": 0.3}
)
```

## 📝 核心术语对照

| 英文术语 | 中文翻译 | 说明 |
|----------|----------|------|
| Population | 种群 | 进化算法中的代码集合 |
| Elite | 精英 | 最优代码的保存策略 |
| Selection | 选择 | 父代选择策略 |
| Variation | 变异 | 代码变异操作 |
| Mutation | 突变 | 单个代码的修改 |
| Crossover | 交叉 | 多个代码的组合 |
| Evaluation | 评估 | 代码质量评估 |
| Fitness | 适应度 | 代码的适应性度量 |
| Generation | 代数 | 进化的代际 |
| Migration | 迁移 | 岛屿间的代码迁移 |
| Feature Space | 特征空间 | 代码特征的组织空间 |
| Controller | 控制器 | 进化过程的协调者 |

## 🏗️ 设计原则

Evolution框架严格遵循现代软件工程原则：

### ✅ 单一职责原则 (SRP)
- 每个类只负责一个明确的功能
- Code类只负责代码表示
- Population类只负责种群管理
- Elite类只负责精英管理

### ✅ 接口隔离原则 (ISP)
- 接口精简，方法职责单一
- 抽象基类定义清晰的接口契约
- 避免臃肿的接口设计

### ✅ 依赖倒置原则 (DIP)
- 高层模块不依赖低层模块，都依赖于抽象
- EvolutionController依赖抽象接口而非具体实现
- 使用工厂函数创建具体实例

### ✅ 开闭原则 (OCP)
- 对扩展开放，对修改关闭
- 可以轻松添加新的选择策略、变异操作、评估器
- 通过工厂函数支持插件式扩展

### ✅ 一致性原则
- 命名和接口设计保持高度一致
- 所有配置类都有validate()方法
- 所有存储类都有相似的CRUD接口

## 🔧 扩展开发

### 添加新的选择策略

```python
from evolution.evolution.selection import Selection

class CustomSelection(Selection):
    """自定义选择策略"""

    def __init__(self, custom_param: float = 1.0):
        self.custom_param = custom_param

    def select(self, candidates: List[Code], count: int = 1, **kwargs) -> List[Code]:
        # 实现自定义选择逻辑
        pass

    def get_config(self) -> Dict[str, Any]:
        return {
            "strategy": "custom",
            "custom_param": self.custom_param
        }
```

### 添加新的变异操作

```python
from evolution.evolution.variation import Mutation

class CustomMutation(Mutation):
    """自定义突变操作"""

    def apply(self, codes: List[Code], **kwargs) -> Code:
        # 实现自定义突变逻辑
        parent = codes[0]
        # ... 突变逻辑 ...
        return mutated_code

    def get_config(self) -> Dict[str, Any]:
        return {"operator": "custom_mutation"}
```

### 添加新的评估器

```python
from evolution.evolution.evaluation import Evaluator

class CustomEvaluator(Evaluator):
    """自定义评估器"""

    def evaluate(self, code: Code, **kwargs) -> Dict[str, float]:
        # 实现自定义评估逻辑
        return {"custom_metric": score}

    def compare(self, metrics_a: Dict[str, float],
               metrics_b: Dict[str, float]) -> int:
        # 实现比较逻辑
        pass
```

## 📊 监控和统计

```python
# 获取详细统计信息
stats = controller.get_stats()
print(f"当前代数: {stats['generation']}")
print(f"总评估次数: {stats['total_evaluations']}")
print(f"运行时间: {stats['runtime']:.2f}秒")

# 种群统计
if 'population' in stats:
    pop_stats = stats['population']
    print(f"种群大小: {pop_stats['total_size']}")
    print(f"岛屿数量: {pop_stats['num_islands']}")

# 精英统计
if 'elite' in stats:
    elite_stats = stats['elite']
    print(f"精英数量: {elite_stats['count']}")
    print(f"最佳适应度: {elite_stats['best_fitness']}")
```

## 💾 数据持久化

### 代码存储

```python
from evolution.data.code_storage import MemoryCodeStorage

# 内存存储（适用于测试）
storage = MemoryCodeStorage()

# 保存代码
storage.save(code)

# 检索代码
retrieved = storage.get(code.id)

# 查询代码
results = storage.find(
    query={"generation": {"$gte": 5}},
    limit=10,
    sort_by="fitness",
    ascending=False
)
```

### 资源存储

```python
from evolution.data.asset_storage import FileSystemAssetStorage

# 文件系统存储
asset_storage = FileSystemAssetStorage("/path/to/assets")

# 保存资源
asset_storage.save(
    code_id=code.id,
    name="source.py",
    data=code.content.encode(),
    content_type="text/python"
)

# 获取资源
data = asset_storage.get(code.id, "source.py")
```

## 🧪 测试

```python
# 运行基本功能测试
python -c "
from evolution.core.code import Code
from evolution.data.code_storage import MemoryCodeStorage

# 测试代码创建
code = Code(content='print(\"hello\")', generation=0)
print(f'✅ 代码创建成功: {code.id}')

# 测试存储
storage = MemoryCodeStorage()
storage.save(code)
retrieved = storage.get(code.id)
print(f'✅ 存储测试成功: {retrieved.id == code.id}')

print('🎉 所有测试通过！')
"
```

## 📈 性能优化建议

1. **批量操作**: 使用批量接口进行大量数据操作
2. **缓存策略**: 合理使用缓存减少重复计算
3. **并行处理**: 利用多进程/多线程加速评估
4. **内存管理**: 及时清理不需要的代码实例
5. **存储优化**: 根据数据量选择合适的存储方案

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- 感谢所有贡献者的努力
- 感谢进化算法研究社区的启发
- 感谢开源社区的支持

## 📞 联系方式

- 项目主页: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 文档: [项目文档]

---

**Evolution Framework** - 让代码进化变得简单而强大 🚀