"""
代码进化的种群管理。

本模块提供使用岛屿模型和迁移策略管理代码种群的类。
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, List, Optional

from ..core.code import Code


@dataclass
class PopulationConfig:
    """
    种群管理的配置。
    
    属性:
        num_islands: 岛屿模型中的岛屿数量
        migration_interval: 迁移之间的代数间隔
        migration_rate: 迁移的种群比例（0.0到1.0）
        max_codes_per_island: 每个岛屿的最大代码数（None表示无限制）
    """
    
    num_islands: int = 1
    migration_interval: int = 10
    migration_rate: float = 0.1
    max_codes_per_island: Optional[int] = None
    
    def validate(self) -> None:
        """
        验证配置。

        抛出:
            ValueError: 如果配置无效
        """
        if self.num_islands < 1:
            raise ValueError("岛屿数量必须至少为1")
        if self.migration_interval < 1:
            raise ValueError("迁移间隔必须至少为1")
        if not 0.0 <= self.migration_rate <= 1.0:
            raise ValueError("迁移率必须在0.0到1.0之间")
        if self.max_codes_per_island is not None and self.max_codes_per_island < 1:
            raise ValueError("每个岛屿的最大代码数必须至少为1")


class Population(ABC):
    """
    管理代码种群的抽象基类。

    支持岛屿模型以维护多样性和并行进化。
    """
    
    def __init__(self, config: PopulationConfig):
        """
        初始化种群。

        参数:
            config: 种群配置
        """
        config.validate()
        self.config = config
        self._generation = 0
    
    @abstractmethod
    def add(self, code: Code, island_id: Optional[int] = None) -> None:
        """
        向种群添加代码。

        参数:
            code: 要添加的代码实例
            island_id: 目标岛屿ID（None表示自动分配）

        抛出:
            ValueError: 如果island_id无效
        """
        pass
    
    @abstractmethod
    def remove(self, code: Code, island_id: Optional[int] = None) -> bool:
        """
        从种群中移除代码。

        参数:
            code: 要移除的代码实例
            island_id: 要搜索的岛屿（None表示所有岛屿）

        返回:
            如果找到并移除代码则返回True，否则返回False
        """
        pass
    
    @abstractmethod
    def sample(self, strategy: str = "tournament",
              island_id: Optional[int] = None,
              count: int = 1, **kwargs) -> List[Code]:
        """
        使用指定策略从种群中采样代码。

        参数:
            strategy: 采样策略（"tournament"、"random"、"best"等）
            island_id: 要采样的岛屿（None表示所有岛屿）
            count: 要采样的代码数量
            **kwargs: 额外的策略特定参数

        返回:
            采样的代码列表

        抛出:
            ValueError: 如果策略未知或参数无效
        """
        pass
    
    @abstractmethod
    def migrate(self) -> None:
        """
        执行岛屿间迁移。

        只有当基于代数和迁移间隔确定需要迁移时才应调用。
        """
        pass
    
    @abstractmethod
    def get_stats(self, island_id: Optional[int] = None) -> Dict:
        """
        获取种群统计信息。

        参数:
            island_id: 要获取统计信息的岛屿（None表示所有岛屿）

        返回:
            包含统计信息的字典，如:
            - 'count': 代码数量
            - 'avg_fitness': 平均适应度
            - 'diversity': 多样性指标
            - 'generation': 当前代数
        """
        pass
    
    @abstractmethod
    def count(self, island_id: Optional[int] = None) -> int:
        """
        获取种群中的代码数量。

        参数:
            island_id: 要计数的岛屿（None表示所有岛屿）

        返回:
            代码数量
        """
        pass
    
    @abstractmethod
    def clear(self, island_id: Optional[int] = None) -> None:
        """
        清空种群中的代码。

        参数:
            island_id: 要清空的岛屿（None表示所有岛屿）
        """
        pass
    
    def get_generation(self) -> int:
        """
        获取当前代数。

        返回:
            当前代数
        """
        return self._generation
    
    def advance_generation(self) -> None:
        """推进到下一代。"""
        self._generation += 1

    def should_migrate(self) -> bool:
        """
        检查是否应该进行迁移。

        返回:
            如果需要迁移则返回True
        """
        return (self._generation > 0 and 
                self._generation % self.config.migration_interval == 0)
    
    def get_island_count(self) -> int:
        """
        获取岛屿数量。

        返回:
            岛屿数量
        """
        return self.config.num_islands
