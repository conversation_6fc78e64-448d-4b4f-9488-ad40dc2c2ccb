"""
代码进化的种群管理。

本模块提供使用岛屿模型和迁移策略管理代码种群的类。
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, List, Optional

from ..core.code import Code


@dataclass
class PopulationConfig:
    """
    种群管理的配置。
    
    属性:
        num_islands: 岛屿模型中的岛屿数量
        migration_interval: 迁移之间的代数间隔
        migration_rate: 迁移的种群比例（0.0到1.0）
        max_codes_per_island: 每个岛屿的最大代码数（None表示无限制）
    """
    
    num_islands: int = 1
    migration_interval: int = 10
    migration_rate: float = 0.1
    max_codes_per_island: Optional[int] = None
    
    def validate(self) -> None:
        """
        验证配置。

        抛出:
            ValueError: 如果配置无效
        """
        if self.num_islands < 1:
            raise ValueError("Number of islands must be at least 1")
        if self.migration_interval < 1:
            raise ValueError("Migration interval must be at least 1")
        if not 0.0 <= self.migration_rate <= 1.0:
            raise ValueError("Migration rate must be between 0.0 and 1.0")
        if self.max_codes_per_island is not None and self.max_codes_per_island < 1:
            raise ValueError("Max codes per island must be at least 1")


class Population(ABC):
    """
    管理代码种群的抽象基类。

    支持岛屿模型以维护多样性和并行进化。
    """
    
    def __init__(self, config: PopulationConfig):
        """
        Initialize the population.
        
        Args:
            config: Population configuration
        """
        config.validate()
        self.config = config
        self._generation = 0
    
    @abstractmethod
    def add(self, code: Code, island_id: Optional[int] = None) -> None:
        """
        Add a code to the population.
        
        Args:
            code: Code instance to add
            island_id: Target island ID (None for automatic assignment)
            
        Raises:
            ValueError: If island_id is invalid
        """
        pass
    
    @abstractmethod
    def remove(self, code: Code, island_id: Optional[int] = None) -> bool:
        """
        Remove a code from the population.
        
        Args:
            code: Code instance to remove
            island_id: Island to search (None for all islands)
            
        Returns:
            True if code was found and removed, False otherwise
        """
        pass
    
    @abstractmethod
    def sample(self, strategy: str = "tournament", 
              island_id: Optional[int] = None, 
              count: int = 1, **kwargs) -> List[Code]:
        """
        Sample codes from the population using specified strategy.
        
        Args:
            strategy: Sampling strategy ("tournament", "random", "best", etc.)
            island_id: Island to sample from (None for all islands)
            count: Number of codes to sample
            **kwargs: Additional strategy-specific parameters
            
        Returns:
            List of sampled codes
            
        Raises:
            ValueError: If strategy is unknown or parameters are invalid
        """
        pass
    
    @abstractmethod
    def migrate(self) -> None:
        """
        Perform migration between islands.
        
        Should only be called when migration is due based on generation count
        and migration interval.
        """
        pass
    
    @abstractmethod
    def get_stats(self, island_id: Optional[int] = None) -> Dict:
        """
        Get population statistics.
        
        Args:
            island_id: Island to get stats for (None for all islands)
            
        Returns:
            Dictionary containing statistics like:
            - 'count': number of codes
            - 'avg_fitness': average fitness
            - 'diversity': diversity metrics
            - 'generation': current generation
        """
        pass
    
    @abstractmethod
    def count(self, island_id: Optional[int] = None) -> int:
        """
        Get number of codes in population.
        
        Args:
            island_id: Island to count (None for all islands)
            
        Returns:
            Number of codes
        """
        pass
    
    @abstractmethod
    def clear(self, island_id: Optional[int] = None) -> None:
        """
        Clear codes from population.
        
        Args:
            island_id: Island to clear (None for all islands)
        """
        pass
    
    def get_generation(self) -> int:
        """
        Get current generation number.
        
        Returns:
            Current generation
        """
        return self._generation
    
    def advance_generation(self) -> None:
        """Advance to next generation."""
        self._generation += 1
    
    def should_migrate(self) -> bool:
        """
        Check if migration should occur.
        
        Returns:
            True if migration is due
        """
        return (self._generation > 0 and 
                self._generation % self.config.migration_interval == 0)
    
    def get_island_count(self) -> int:
        """
        Get number of islands.
        
        Returns:
            Number of islands
        """
        return self.config.num_islands
