"""
Elite management for code evolution.

This module provides classes for managing elite codes - the best
performing codes that should be preserved across generations.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, List, Tuple

from ..core.code import Code


@dataclass
class EliteConfig:
    """
    Configuration for elite management.
    
    Attributes:
        max_size: Maximum number of elite codes to maintain
        min_improvement: Minimum improvement required to replace an elite
    """
    
    max_size: int = 100
    min_improvement: float = 0.01
    
    def validate(self) -> None:
        """
        Validate the configuration.
        
        Raises:
            ValueError: If configuration is invalid
        """
        if self.max_size < 1:
            raise ValueError("Max size must be at least 1")
        if self.min_improvement < 0:
            raise ValueError("Min improvement must be non-negative")


class Elite(ABC):
    """
    Abstract base class for managing elite codes.
    
    Elite codes are the best performing codes that should be preserved
    and potentially used for breeding in future generations.
    """
    
    def __init__(self, config: EliteConfig):
        """
        Initialize the elite manager.
        
        Args:
            config: Elite configuration
        """
        config.validate()
        self.config = config
    
    @abstractmethod
    def add(self, code: Code) -> bool:
        """
        Add a code to the elite set.
        
        The code will only be added if it meets the quality criteria
        and there's space or it can replace an existing elite.
        
        Args:
            code: Code instance to add
            
        Returns:
            True if code was added to elite set, False otherwise
        """
        pass
    
    @abstractmethod
    def remove(self, code: Code) -> bool:
        """
        Remove a code from the elite set.
        
        Args:
            code: Code instance to remove
            
        Returns:
            True if code was found and removed, False otherwise
        """
        pass
    
    @abstractmethod
    def sample(self, count: int = 10, **kwargs) -> List[Code]:
        """
        Sample codes from the elite set.
        
        Args:
            count: Number of codes to sample
            **kwargs: Additional sampling parameters
            
        Returns:
            List of sampled elite codes
            
        Raises:
            ValueError: If count is invalid
        """
        pass
    
    @abstractmethod
    def find_by_features(self, 
                        feature_ranges: Dict[str, Tuple[float, float]]) -> List[Code]:
        """
        Find elite codes within specified feature ranges.
        
        Args:
            feature_ranges: Dictionary mapping feature names to (min, max) ranges
            
        Returns:
            List of elite codes matching the feature criteria
        """
        pass
    
    @abstractmethod
    def contains(self, code: Code) -> bool:
        """
        Check if a code is in the elite set.
        
        Args:
            code: Code instance to check
            
        Returns:
            True if code is in elite set, False otherwise
        """
        pass
    
    @abstractmethod
    def get_best(self, metric: str, count: int = 1) -> List[Code]:
        """
        Get the best codes according to a specific metric.
        
        Args:
            metric: Name of the metric to optimize
            count: Number of best codes to return
            
        Returns:
            List of best codes sorted by the metric
            
        Raises:
            ValueError: If metric is unknown or count is invalid
        """
        pass
    
    @abstractmethod
    def get_stats(self) -> Dict:
        """
        Get statistics about the elite set.
        
        Returns:
            Dictionary containing statistics like:
            - 'count': number of elite codes
            - 'avg_metrics': average values for each metric
            - 'best_metrics': best values for each metric
            - 'diversity': diversity metrics
        """
        pass
    
    @abstractmethod
    def count(self) -> int:
        """
        Get number of codes in elite set.
        
        Returns:
            Number of elite codes
        """
        pass
    
    @abstractmethod
    def clear(self) -> None:
        """Remove all codes from elite set."""
        pass
    
    def is_full(self) -> bool:
        """
        Check if elite set is at maximum capacity.
        
        Returns:
            True if elite set is full, False otherwise
        """
        return self.count() >= self.config.max_size
    
    def get_capacity(self) -> int:
        """
        Get maximum capacity of elite set.
        
        Returns:
            Maximum number of elite codes
        """
        return self.config.max_size
