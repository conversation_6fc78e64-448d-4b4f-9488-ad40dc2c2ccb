"""
代码进化的精英管理。

本模块提供管理精英代码的类 - 应该在代际间保存的最佳性能代码。
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, List, Tuple

from ..core.code import Code


@dataclass
class EliteConfig:
    """
    精英管理配置。

    属性:
        max_size: 维护的精英代码最大数量
        min_improvement: 替换精英所需的最小改进
    """
    
    max_size: int = 100
    min_improvement: float = 0.01
    
    def validate(self) -> None:
        """
        验证配置。

        抛出:
            ValueError: 如果配置无效
        """
        if self.max_size < 1:
            raise ValueError("最大大小必须至少为1")
        if self.min_improvement < 0:
            raise ValueError("最小改进必须为非负数")


class Elite(ABC):
    """
    管理精英代码的抽象基类。

    精英代码是应该被保存并可能在未来代际中用于繁殖的最佳性能代码。
    """
    
    def __init__(self, config: EliteConfig):
        """
        初始化精英管理器。

        参数:
            config: 精英配置
        """
        config.validate()
        self.config = config
    
    @abstractmethod
    def add(self, code: Code) -> bool:
        """
        向精英集合添加代码。

        只有当代码满足质量标准且有空间或可以替换现有精英时，
        代码才会被添加。

        参数:
            code: 要添加的代码实例

        返回:
            如果代码被添加到精英集合则返回True，否则返回False
        """
        pass
    
    @abstractmethod
    def remove(self, code: Code) -> bool:
        """
        Remove a code from the elite set.
        
        Args:
            code: Code instance to remove
            
        Returns:
            True if code was found and removed, False otherwise
        """
        pass
    
    @abstractmethod
    def sample(self, count: int = 10, **kwargs) -> List[Code]:
        """
        Sample codes from the elite set.
        
        Args:
            count: Number of codes to sample
            **kwargs: Additional sampling parameters
            
        Returns:
            List of sampled elite codes
            
        Raises:
            ValueError: If count is invalid
        """
        pass
    
    @abstractmethod
    def find_by_features(self, 
                        feature_ranges: Dict[str, Tuple[float, float]]) -> List[Code]:
        """
        Find elite codes within specified feature ranges.
        
        Args:
            feature_ranges: Dictionary mapping feature names to (min, max) ranges
            
        Returns:
            List of elite codes matching the feature criteria
        """
        pass
    
    @abstractmethod
    def contains(self, code: Code) -> bool:
        """
        Check if a code is in the elite set.
        
        Args:
            code: Code instance to check
            
        Returns:
            True if code is in elite set, False otherwise
        """
        pass
    
    @abstractmethod
    def get_best(self, metric: str, count: int = 1) -> List[Code]:
        """
        Get the best codes according to a specific metric.
        
        Args:
            metric: Name of the metric to optimize
            count: Number of best codes to return
            
        Returns:
            List of best codes sorted by the metric
            
        Raises:
            ValueError: If metric is unknown or count is invalid
        """
        pass
    
    @abstractmethod
    def get_stats(self) -> Dict:
        """
        Get statistics about the elite set.
        
        Returns:
            Dictionary containing statistics like:
            - 'count': number of elite codes
            - 'avg_metrics': average values for each metric
            - 'best_metrics': best values for each metric
            - 'diversity': diversity metrics
        """
        pass
    
    @abstractmethod
    def count(self) -> int:
        """
        Get number of codes in elite set.
        
        Returns:
            Number of elite codes
        """
        pass
    
    @abstractmethod
    def clear(self) -> None:
        """Remove all codes from elite set."""
        pass
    
    def is_full(self) -> bool:
        """
        Check if elite set is at maximum capacity.
        
        Returns:
            True if elite set is full, False otherwise
        """
        return self.count() >= self.config.max_size
    
    def get_capacity(self) -> int:
        """
        Get maximum capacity of elite set.
        
        Returns:
            Maximum number of elite codes
        """
        return self.config.max_size
