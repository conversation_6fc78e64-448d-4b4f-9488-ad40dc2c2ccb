"""
代码进化的精英管理。

本模块提供管理精英代码的类 - 应该在代际间保存的最佳性能代码。
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, List, Tuple

from ..core.code import Code


@dataclass
class EliteConfig:
    """
    精英管理配置。

    属性:
        max_size: 维护的精英代码最大数量
        min_improvement: 替换精英所需的最小改进
    """
    
    max_size: int = 100
    min_improvement: float = 0.01
    
    def validate(self) -> None:
        """
        验证配置。

        抛出:
            ValueError: 如果配置无效
        """
        if self.max_size < 1:
            raise ValueError("最大大小必须至少为1")
        if self.min_improvement < 0:
            raise ValueError("最小改进必须为非负数")


class Elite(ABC):
    """
    管理精英代码的抽象基类。

    精英代码是应该被保存并可能在未来代际中用于繁殖的最佳性能代码。
    """
    
    def __init__(self, config: EliteConfig):
        """
        初始化精英管理器。

        参数:
            config: 精英配置
        """
        config.validate()
        self.config = config
    
    @abstractmethod
    def add(self, code: Code) -> bool:
        """
        向精英集合添加代码。

        只有当代码满足质量标准且有空间或可以替换现有精英时，
        代码才会被添加。

        参数:
            code: 要添加的代码实例

        返回:
            如果代码被添加到精英集合则返回True，否则返回False
        """
        pass
    
    @abstractmethod
    def remove(self, code: Code) -> bool:
        """
        从精英集合中移除代码。

        参数:
            code: 要移除的代码实例

        返回:
            如果找到并移除代码则返回True，否则返回False
        """
        pass
    
    @abstractmethod
    def sample(self, count: int = 10, **kwargs) -> List[Code]:
        """
        从精英集合中采样代码。

        参数:
            count: 要采样的代码数量
            **kwargs: 额外的采样参数

        返回:
            采样的精英代码列表

        抛出:
            ValueError: 如果count无效
        """
        pass
    
    @abstractmethod
    def find_by_features(self,
                        feature_ranges: Dict[str, Tuple[float, float]]) -> List[Code]:
        """
        在指定特征范围内查找精英代码。

        参数:
            feature_ranges: 将特征名称映射到(最小值, 最大值)范围的字典

        返回:
            匹配特征条件的精英代码列表
        """
        pass
    
    @abstractmethod
    def contains(self, code: Code) -> bool:
        """
        检查代码是否在精英集合中。

        参数:
            code: 要检查的代码实例

        返回:
            如果代码在精英集合中则返回True，否则返回False
        """
        pass
    
    @abstractmethod
    def get_best(self, metric: str, count: int = 1) -> List[Code]:
        """
        根据特定指标获取最佳代码。

        参数:
            metric: 要优化的指标名称
            count: 要返回的最佳代码数量

        返回:
            按指标排序的最佳代码列表

        抛出:
            ValueError: 如果指标未知或count无效
        """
        pass
    
    @abstractmethod
    def get_stats(self) -> Dict:
        """
        获取精英集合的统计信息。

        返回:
            包含统计信息的字典，如:
            - 'count': 精英代码数量
            - 'avg_metrics': 每个指标的平均值
            - 'best_metrics': 每个指标的最佳值
            - 'diversity': 多样性指标
        """
        pass
    
    @abstractmethod
    def count(self) -> int:
        """
        获取精英集合中的代码数量。

        返回:
            精英代码数量
        """
        pass
    
    @abstractmethod
    def clear(self) -> None:
        """从精英集合中移除所有代码。"""
        pass

    def is_full(self) -> bool:
        """
        检查精英集合是否已达到最大容量。

        返回:
            如果精英集合已满则返回True，否则返回False
        """
        return self.count() >= self.config.max_size
    
    def get_capacity(self) -> int:
        """
        获取精英集合的最大容量。

        返回:
            精英代码的最大数量
        """
        return self.config.max_size
