#!/usr/bin/env python3
"""
Example usage of the Evolution Framework.

This script demonstrates how to use the main components of the evolution framework
to create and manipulate code instances.
"""

from evolution import Code, EvolutionConfig
from evolution.data.code_storage import MemoryCodeStorage
from evolution.data.asset_storage import FileSystemAssetStorage


def main():
    """Demonstrate basic framework usage."""
    print("Evolution Framework Example")
    print("=" * 40)
    
    # 1. Create a code instance
    print("\n1. Creating a Code instance:")
    code = Code(
        content="def hello():\n    print('Hello, World!')",
        metrics={"performance": 0.95, "readability": 0.8},
        features={"complexity": 0.2, "length": 0.3},
        metadata={"language": "python", "author": "evolution"}
    )
    print(f"Created code with ID: {code.id}")
    print(f"Content preview: {code.content[:30]}...")
    print(f"Metrics: {code.metrics}")
    print(f"Features: {code.features}")
    
    # 2. Test code storage
    print("\n2. Testing code storage:")
    storage = MemoryCodeStorage()
    storage.save(code)
    print(f"Saved code to storage")
    
    retrieved = storage.get(code.id)
    print(f"Retrieved code: {retrieved.id if retrieved else 'None'}")
    print(f"Storage stats: {storage.get_stats()}")
    
    # 3. Test asset storage
    print("\n3. Testing asset storage:")
    asset_storage = FileSystemAssetStorage("./assets")
    
    # Save a text asset
    asset_data = "This is a test asset for the code"
    asset_storage.save(code.id, "readme.txt", asset_data, "text/plain")
    print(f"Saved asset for code {code.id}")
    
    # List assets
    assets = asset_storage.list(code.id)
    print(f"Assets for code: {assets}")
    
    # Retrieve asset
    retrieved_asset = asset_storage.get(code.id, "readme.txt")
    if retrieved_asset:
        print(f"Retrieved asset content: {retrieved_asset.decode()}")
    
    # 4. Test configuration
    print("\n4. Testing configuration:")
    config = EvolutionConfig(
        population={
            "num_islands": 2,
            "migration_interval": 10,
            "migration_rate": 0.1
        },
        elite={
            "max_size": 50,
            "min_improvement": 0.01
        },
        selection={
            "strategy": "tournament",
            "tournament_size": 3
        },
        variation={
            "operator": "point_mutation",
            "mutation_rate": 0.1
        },
        evaluation={
            "evaluator": "composite",
            "metrics": ["performance", "quality"]
        }
    )
    print("Created evolution configuration:")
    print(f"- Population islands: {config.population['num_islands']}")
    print(f"- Elite size: {config.elite['max_size']}")
    print(f"- Selection strategy: {config.selection['strategy']}")
    print(f"- Variation operator: {config.variation['operator']}")
    print(f"- Evaluator: {config.evaluation['evaluator']}")
    
    # 5. Test serialization
    print("\n5. Testing serialization:")
    code_dict = code.to_dict()
    print(f"Code serialized to dict with {len(code_dict)} fields")
    
    restored_code = Code.from_dict(code_dict)
    print(f"Code restored from dict: {restored_code.id}")
    print(f"Content matches: {restored_code.content == code.content}")
    print(f"Metrics match: {restored_code.metrics == code.metrics}")
    
    print("\n" + "=" * 40)
    print("Example completed successfully!")


if __name__ == "__main__":
    main()
