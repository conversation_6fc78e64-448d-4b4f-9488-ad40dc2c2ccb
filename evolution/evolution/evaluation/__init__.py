"""
代码进化的评估策略。

本模块提供各种评估策略，用于在进化过程中评估代码的质量和适应度。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional

from ...core.code import Code


class Evaluator(ABC):
    """
    代码评估器的抽象基类。

    评估器评估代码质量并分配可用于选择和比较的指标。
    """
    
    @abstractmethod
    def evaluate(self, code: Code, **kwargs) -> Dict[str, float]:
        """
        评估代码并返回指标。

        参数:
            code: 要评估的代码实例
            **kwargs: 额外的评估参数

        返回:
            将指标名称映射到值的字典

        抛出:
            EvaluationError: 如果评估失败
        """
        pass
    
    @abstractmethod
    def compare(self, metrics_a: Dict[str, float], 
               metrics_b: Dict[str, float]) -> int:
        """
        比较两组指标。

        参数:
            metrics_a: 第一组指标
            metrics_b: 第二组指标

        返回:
            如果a < b返回-1，如果a == b返回0，如果a > b返回1
        """
        pass
    
    @abstractmethod
    def get_config(self) -> Dict[str, Any]:
        """
        获取此评估器的配置参数。

        返回:
            配置参数字典
        """
        pass
    
    @abstractmethod
    def get_metric_names(self) -> List[str]:
        """
        获取此评估器产生的指标名称。

        返回:
            指标名称列表
        """
        pass


class EvaluationError(Exception):
    """代码评估失败时抛出的异常。"""
    pass


class PerformanceEvaluator(Evaluator):
    """
    测量代码性能指标的评估器。

    评估执行时间、内存使用和其他性能特征。
    """
    
    def __init__(self, timeout: float = 10.0, memory_limit: Optional[int] = None):
        """
        初始化性能评估器。

        参数:
            timeout: 最大执行时间（秒）
            memory_limit: 最大内存使用量（字节）（None表示无限制）
        """
        if timeout <= 0:
            raise ValueError("超时时间必须为正数")
        if memory_limit is not None and memory_limit <= 0:
            raise ValueError("内存限制必须为正数")
        
        self.timeout = timeout
        self.memory_limit = memory_limit
    
    def evaluate(self, code: Code, **kwargs) -> Dict[str, float]:
        """评估代码性能。"""
        # 具体实现将在具体实现中提供
        raise NotImplementedError("需要实现")

    def compare(self, metrics_a: Dict[str, float],
               metrics_b: Dict[str, float]) -> int:
        """比较性能指标（时间/内存越低越好）。"""
        # 具体实现将在具体实现中提供
        raise NotImplementedError("需要实现")

    def get_config(self) -> Dict[str, Any]:
        """获取性能评估器配置。"""
        return {
            "evaluator": "performance",
            "timeout": self.timeout,
            "memory_limit": self.memory_limit
        }

    def get_metric_names(self) -> List[str]:
        """获取性能指标名称。"""
        return ["execution_time", "memory_usage", "cpu_usage"]


class QualityEvaluator(Evaluator):
    """
    测量代码质量指标的评估器。

    评估可读性、可维护性、复杂性和其他质量方面。
    """
    
    def __init__(self, metrics: List[str] = None):
        """
        初始化质量评估器。

        参数:
            metrics: 要计算的质量指标列表
        """
        if metrics is None:
            metrics = ["complexity", "readability", "maintainability"]
        self.metrics = metrics
    
    def evaluate(self, code: Code, **kwargs) -> Dict[str, float]:
        """评估代码质量。"""
        # 具体实现将在具体实现中提供
        raise NotImplementedError("需要实现")

    def compare(self, metrics_a: Dict[str, float],
               metrics_b: Dict[str, float]) -> int:
        """比较质量指标（越高越好）。"""
        # 具体实现将在具体实现中提供
        raise NotImplementedError("需要实现")

    def get_config(self) -> Dict[str, Any]:
        """获取质量评估器配置。"""
        return {
            "evaluator": "quality",
            "metrics": self.metrics.copy()
        }

    def get_metric_names(self) -> List[str]:
        """获取质量指标名称。"""
        return self.metrics.copy()


class CompositeEvaluator(Evaluator):
    """
    组合多个子评估器的评估器。

    允许组合不同类型的评估（性能、质量等）并配置权重。
    """
    
    def __init__(self, evaluators: Dict[str, Evaluator], 
                 weights: Optional[Dict[str, float]] = None):
        """
        初始化组合评估器。

        参数:
            evaluators: 将名称映射到评估器实例的字典
            weights: 将评估器名称映射到权重的字典
        """
        if not evaluators:
            raise ValueError("必须提供至少一个评估器")

        self.evaluators = evaluators
        if weights is None:
            weights = {name: 1.0 for name in evaluators.keys()}
        self.weights = weights

        # 验证权重
        for name in evaluators.keys():
            if name not in weights:
                raise ValueError(f"未为评估器 '{name}' 提供权重")
    
    def evaluate(self, code: Code, **kwargs) -> Dict[str, float]:
        """使用所有子评估器评估代码。"""
        # 具体实现将在具体实现中提供
        raise NotImplementedError("需要实现")

    def compare(self, metrics_a: Dict[str, float],
               metrics_b: Dict[str, float]) -> int:
        """比较组合指标。"""
        # 具体实现将在具体实现中提供
        raise NotImplementedError("需要实现")

    def get_config(self) -> Dict[str, Any]:
        """获取组合评估器配置。"""
        return {
            "evaluator": "composite",
            "evaluators": {name: eval.get_config() for name, eval in self.evaluators.items()},
            "weights": self.weights.copy()
        }

    def get_metric_names(self) -> List[str]:
        """从子评估器获取所有指标名称。"""
        names = []
        for evaluator in self.evaluators.values():
            names.extend(evaluator.get_metric_names())
        return names


def create_evaluator(name: str, **kwargs) -> Evaluator:
    """
    创建评估器的工厂函数。

    参数:
        name: 评估器的名称
        **kwargs: 评估器特定参数

    返回:
        评估器实例

    抛出:
        ValueError: 如果评估器名称未知
    """
    evaluators = {
        "performance": PerformanceEvaluator,
        "quality": QualityEvaluator,
        "composite": CompositeEvaluator
    }
    
    if name not in evaluators:
        available = ", ".join(evaluators.keys())
        raise ValueError(f"未知的评估器 '{name}'。可用评估器: {available}")
    
    return evaluators[name](**kwargs)
