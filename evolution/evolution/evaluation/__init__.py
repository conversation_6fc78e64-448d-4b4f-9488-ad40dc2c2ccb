"""
Evaluation strategies for code evolution.

This module provides various evaluation strategies for assessing
the quality and fitness of codes during evolution.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional

from ...core.code import Code


class Evaluator(ABC):
    """
    Abstract base class for code evaluators.
    
    Evaluators assess the quality of codes and assign metrics
    that can be used for selection and comparison.
    """
    
    @abstractmethod
    def evaluate(self, code: Code, **kwargs) -> Dict[str, float]:
        """
        Evaluate a code and return metrics.
        
        Args:
            code: Code instance to evaluate
            **kwargs: Additional evaluation parameters
            
        Returns:
            Dictionary mapping metric names to values
            
        Raises:
            EvaluationError: If evaluation fails
        """
        pass
    
    @abstractmethod
    def compare(self, metrics_a: Dict[str, float], 
               metrics_b: Dict[str, float]) -> int:
        """
        Compare two sets of metrics.
        
        Args:
            metrics_a: First set of metrics
            metrics_b: Second set of metrics
            
        Returns:
            -1 if a < b, 0 if a == b, 1 if a > b
        """
        pass
    
    @abstractmethod
    def get_config(self) -> Dict[str, Any]:
        """
        Get configuration parameters for this evaluator.
        
        Returns:
            Dictionary of configuration parameters
        """
        pass
    
    @abstractmethod
    def get_metric_names(self) -> List[str]:
        """
        Get names of metrics produced by this evaluator.
        
        Returns:
            List of metric names
        """
        pass


class EvaluationError(Exception):
    """Exception raised when code evaluation fails."""
    pass


class PerformanceEvaluator(Evaluator):
    """
    Evaluator that measures code performance metrics.
    
    Assesses execution time, memory usage, and other performance characteristics.
    """
    
    def __init__(self, timeout: float = 10.0, memory_limit: Optional[int] = None):
        """
        Initialize performance evaluator.
        
        Args:
            timeout: Maximum execution time in seconds
            memory_limit: Maximum memory usage in bytes (None for unlimited)
        """
        if timeout <= 0:
            raise ValueError("Timeout must be positive")
        if memory_limit is not None and memory_limit <= 0:
            raise ValueError("Memory limit must be positive")
        
        self.timeout = timeout
        self.memory_limit = memory_limit
    
    def evaluate(self, code: Code, **kwargs) -> Dict[str, float]:
        """Evaluate code performance."""
        # Implementation would be provided in concrete implementation
        raise NotImplementedError("Implementation required")
    
    def compare(self, metrics_a: Dict[str, float], 
               metrics_b: Dict[str, float]) -> int:
        """Compare performance metrics (lower is better for time/memory)."""
        # Implementation would be provided in concrete implementation
        raise NotImplementedError("Implementation required")
    
    def get_config(self) -> Dict[str, Any]:
        """Get performance evaluator configuration."""
        return {
            "evaluator": "performance",
            "timeout": self.timeout,
            "memory_limit": self.memory_limit
        }
    
    def get_metric_names(self) -> List[str]:
        """Get performance metric names."""
        return ["execution_time", "memory_usage", "cpu_usage"]


class QualityEvaluator(Evaluator):
    """
    Evaluator that measures code quality metrics.
    
    Assesses readability, maintainability, complexity, and other quality aspects.
    """
    
    def __init__(self, metrics: List[str] = None):
        """
        Initialize quality evaluator.
        
        Args:
            metrics: List of quality metrics to compute
        """
        if metrics is None:
            metrics = ["complexity", "readability", "maintainability"]
        self.metrics = metrics
    
    def evaluate(self, code: Code, **kwargs) -> Dict[str, float]:
        """Evaluate code quality."""
        # Implementation would be provided in concrete implementation
        raise NotImplementedError("Implementation required")
    
    def compare(self, metrics_a: Dict[str, float], 
               metrics_b: Dict[str, float]) -> int:
        """Compare quality metrics (higher is better)."""
        # Implementation would be provided in concrete implementation
        raise NotImplementedError("Implementation required")
    
    def get_config(self) -> Dict[str, Any]:
        """Get quality evaluator configuration."""
        return {
            "evaluator": "quality",
            "metrics": self.metrics.copy()
        }
    
    def get_metric_names(self) -> List[str]:
        """Get quality metric names."""
        return self.metrics.copy()


class CompositeEvaluator(Evaluator):
    """
    Evaluator that combines multiple sub-evaluators.
    
    Allows combining different types of evaluation (performance, quality, etc.)
    with configurable weights.
    """
    
    def __init__(self, evaluators: Dict[str, Evaluator], 
                 weights: Optional[Dict[str, float]] = None):
        """
        Initialize composite evaluator.
        
        Args:
            evaluators: Dictionary mapping names to evaluator instances
            weights: Dictionary mapping evaluator names to weights
        """
        if not evaluators:
            raise ValueError("At least one evaluator must be provided")
        
        self.evaluators = evaluators
        if weights is None:
            weights = {name: 1.0 for name in evaluators.keys()}
        self.weights = weights
        
        # Validate weights
        for name in evaluators.keys():
            if name not in weights:
                raise ValueError(f"Weight not provided for evaluator '{name}'")
    
    def evaluate(self, code: Code, **kwargs) -> Dict[str, float]:
        """Evaluate code using all sub-evaluators."""
        # Implementation would be provided in concrete implementation
        raise NotImplementedError("Implementation required")
    
    def compare(self, metrics_a: Dict[str, float], 
               metrics_b: Dict[str, float]) -> int:
        """Compare composite metrics."""
        # Implementation would be provided in concrete implementation
        raise NotImplementedError("Implementation required")
    
    def get_config(self) -> Dict[str, Any]:
        """Get composite evaluator configuration."""
        return {
            "evaluator": "composite",
            "evaluators": {name: eval.get_config() for name, eval in self.evaluators.items()},
            "weights": self.weights.copy()
        }
    
    def get_metric_names(self) -> List[str]:
        """Get all metric names from sub-evaluators."""
        names = []
        for evaluator in self.evaluators.values():
            names.extend(evaluator.get_metric_names())
        return names


def create_evaluator(name: str, **kwargs) -> Evaluator:
    """
    Factory function for creating evaluators.
    
    Args:
        name: Name of the evaluator
        **kwargs: Evaluator-specific parameters
        
    Returns:
        Evaluator instance
        
    Raises:
        ValueError: If evaluator name is unknown
    """
    evaluators = {
        "performance": PerformanceEvaluator,
        "quality": QualityEvaluator,
        "composite": CompositeEvaluator
    }
    
    if name not in evaluators:
        available = ", ".join(evaluators.keys())
        raise ValueError(f"Unknown evaluator '{name}'. Available: {available}")
    
    return evaluators[name](**kwargs)
