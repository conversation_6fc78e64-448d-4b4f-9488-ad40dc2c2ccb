"""
进化框架 - 进化模块

本模块包含核心进化算法，包括选择、变异和评估策略。
"""

# Avoid circular imports - import these modules directly when needed
# from .selection import Selection, create_selection
# from .variation import Variation, create_variation
# from .evaluation import Evaluator, create_evaluator

__all__ = [
    # Commented out to avoid circular imports
    # 'Selection',
    # 'create_selection',
    # 'Variation',
    # 'create_variation',
    # 'Evaluator',
    # 'create_evaluator'
]
