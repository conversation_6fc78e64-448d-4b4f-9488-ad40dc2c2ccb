"""
Evolution Framework - Evolution Module

This module contains the core evolution algorithms including
selection, variation, and evaluation strategies.
"""

# Avoid circular imports - import these modules directly when needed
# from .selection import Selection, create_selection
# from .variation import Variation, create_variation
# from .evaluation import Evaluator, create_evaluator

__all__ = [
    # Commented out to avoid circular imports
    # 'Selection',
    # 'create_selection',
    # 'Variation',
    # 'create_variation',
    # 'Evaluator',
    # 'create_evaluator'
]
