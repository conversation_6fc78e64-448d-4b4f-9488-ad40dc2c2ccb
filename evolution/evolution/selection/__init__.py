"""
Selection strategies for code evolution.

This module provides various selection strategies for choosing
parent codes for reproduction in the evolution process.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any

from ...core.code import Code


class Selection(ABC):
    """
    Abstract base class for selection strategies.
    
    Selection strategies determine which codes from the population
    should be chosen as parents for creating new offspring.
    """
    
    @abstractmethod
    def select(self, candidates: List[Code], count: int = 1, **kwargs) -> List[Code]:
        """
        Select codes from candidates.
        
        Args:
            candidates: List of candidate codes to select from
            count: Number of codes to select
            **kwargs: Additional strategy-specific parameters
            
        Returns:
            List of selected codes
            
        Raises:
            ValueError: If count is invalid or candidates is empty
        """
        pass
    
    @abstractmethod
    def get_config(self) -> Dict[str, Any]:
        """
        Get configuration parameters for this selection strategy.
        
        Returns:
            Dictionary of configuration parameters
        """
        pass


class TournamentSelection(Selection):
    """
    Tournament selection strategy.
    
    Selects codes by running tournaments between randomly chosen
    candidates and picking the best from each tournament.
    """
    
    def __init__(self, tournament_size: int = 3, metric: str = "fitness"):
        """
        Initialize tournament selection.
        
        Args:
            tournament_size: Number of candidates per tournament
            metric: Metric to use for comparison
        """
        if tournament_size < 1:
            raise ValueError("Tournament size must be at least 1")
        self.tournament_size = tournament_size
        self.metric = metric
    
    def select(self, candidates: List[Code], count: int = 1, **kwargs) -> List[Code]:
        """Select codes using tournament selection."""
        # Implementation would be provided in concrete implementation
        raise NotImplementedError("Implementation required")
    
    def get_config(self) -> Dict[str, Any]:
        """Get tournament selection configuration."""
        return {
            "strategy": "tournament",
            "tournament_size": self.tournament_size,
            "metric": self.metric
        }


class RouletteWheelSelection(Selection):
    """
    Roulette wheel (fitness proportionate) selection strategy.
    
    Selects codes with probability proportional to their fitness values.
    """
    
    def __init__(self, metric: str = "fitness", minimize: bool = False):
        """
        Initialize roulette wheel selection.
        
        Args:
            metric: Metric to use for selection probability
            minimize: Whether to minimize the metric (default: maximize)
        """
        self.metric = metric
        self.minimize = minimize
    
    def select(self, candidates: List[Code], count: int = 1, **kwargs) -> List[Code]:
        """Select codes using roulette wheel selection."""
        # Implementation would be provided in concrete implementation
        raise NotImplementedError("Implementation required")
    
    def get_config(self) -> Dict[str, Any]:
        """Get roulette wheel selection configuration."""
        return {
            "strategy": "roulette_wheel",
            "metric": self.metric,
            "minimize": self.minimize
        }


class RankSelection(Selection):
    """
    Rank-based selection strategy.
    
    Selects codes based on their rank rather than absolute fitness values.
    """
    
    def __init__(self, metric: str = "fitness", pressure: float = 1.5):
        """
        Initialize rank selection.
        
        Args:
            metric: Metric to use for ranking
            pressure: Selection pressure (1.0 = uniform, >1.0 = more selective)
        """
        if pressure < 1.0:
            raise ValueError("Selection pressure must be at least 1.0")
        self.metric = metric
        self.pressure = pressure
    
    def select(self, candidates: List[Code], count: int = 1, **kwargs) -> List[Code]:
        """Select codes using rank selection."""
        # Implementation would be provided in concrete implementation
        raise NotImplementedError("Implementation required")
    
    def get_config(self) -> Dict[str, Any]:
        """Get rank selection configuration."""
        return {
            "strategy": "rank",
            "metric": self.metric,
            "pressure": self.pressure
        }


def create_selection(strategy: str, **kwargs) -> Selection:
    """
    Factory function for creating selection strategies.
    
    Args:
        strategy: Name of the selection strategy
        **kwargs: Strategy-specific parameters
        
    Returns:
        Selection instance
        
    Raises:
        ValueError: If strategy is unknown
    """
    strategies = {
        "tournament": TournamentSelection,
        "roulette_wheel": RouletteWheelSelection,
        "rank": RankSelection
    }
    
    if strategy not in strategies:
        available = ", ".join(strategies.keys())
        raise ValueError(f"Unknown selection strategy '{strategy}'. Available: {available}")
    
    return strategies[strategy](**kwargs)
