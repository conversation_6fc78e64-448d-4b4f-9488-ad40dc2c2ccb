"""
Variation operators for code evolution.

This module provides various variation operators (mutation, crossover, etc.)
for creating new code variants during the evolution process.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any

from ...core.code import Code


class Variation(ABC):
    """
    Abstract base class for variation operators.
    
    Variation operators create new codes by modifying or combining
    existing codes through operations like mutation and crossover.
    """
    
    @abstractmethod
    def apply(self, codes: List[Code], **kwargs) -> Code:
        """
        Apply variation operator to input codes.
        
        Args:
            codes: List of parent codes (1 for mutation, 2+ for crossover)
            **kwargs: Additional operator-specific parameters
            
        Returns:
            New code created by applying the variation operator
            
        Raises:
            ValueError: If number of input codes is invalid for this operator
        """
        pass
    
    @abstractmethod
    def get_config(self) -> Dict[str, Any]:
        """
        Get configuration parameters for this variation operator.
        
        Returns:
            Dictionary of configuration parameters
        """
        pass
    
    @abstractmethod
    def get_arity(self) -> int:
        """
        Get the number of parent codes required by this operator.
        
        Returns:
            Number of required parent codes
        """
        pass


class Mutation(Variation):
    """
    Abstract base class for mutation operators.
    
    Mutation operators modify a single parent code to create offspring.
    """
    
    def get_arity(self) -> int:
        """Mutation requires exactly one parent."""
        return 1


class Crossover(Variation):
    """
    Abstract base class for crossover operators.
    
    Crossover operators combine multiple parent codes to create offspring.
    """
    
    def get_arity(self) -> int:
        """Crossover requires at least two parents."""
        return 2


class PointMutation(Mutation):
    """
    Point mutation operator.
    
    Makes small random changes to the code content.
    """
    
    def __init__(self, mutation_rate: float = 0.1, mutation_strength: float = 0.1):
        """
        Initialize point mutation.
        
        Args:
            mutation_rate: Probability of mutation occurring
            mutation_strength: Strength/magnitude of mutations
        """
        if not 0.0 <= mutation_rate <= 1.0:
            raise ValueError("Mutation rate must be between 0.0 and 1.0")
        if mutation_strength < 0.0:
            raise ValueError("Mutation strength must be non-negative")
        
        self.mutation_rate = mutation_rate
        self.mutation_strength = mutation_strength
    
    def apply(self, codes: List[Code], **kwargs) -> Code:
        """Apply point mutation to a single parent code."""
        if len(codes) != 1:
            raise ValueError("Point mutation requires exactly one parent code")
        # Implementation would be provided in concrete implementation
        raise NotImplementedError("Implementation required")
    
    def get_config(self) -> Dict[str, Any]:
        """Get point mutation configuration."""
        return {
            "operator": "point_mutation",
            "mutation_rate": self.mutation_rate,
            "mutation_strength": self.mutation_strength
        }


class UniformCrossover(Crossover):
    """
    Uniform crossover operator.
    
    Combines two parent codes by randomly selecting elements from each.
    """
    
    def __init__(self, crossover_rate: float = 0.5):
        """
        Initialize uniform crossover.
        
        Args:
            crossover_rate: Probability of selecting from first parent
        """
        if not 0.0 <= crossover_rate <= 1.0:
            raise ValueError("Crossover rate must be between 0.0 and 1.0")
        self.crossover_rate = crossover_rate
    
    def apply(self, codes: List[Code], **kwargs) -> Code:
        """Apply uniform crossover to two parent codes."""
        if len(codes) != 2:
            raise ValueError("Uniform crossover requires exactly two parent codes")
        # Implementation would be provided in concrete implementation
        raise NotImplementedError("Implementation required")
    
    def get_config(self) -> Dict[str, Any]:
        """Get uniform crossover configuration."""
        return {
            "operator": "uniform_crossover",
            "crossover_rate": self.crossover_rate
        }


class SemanticMutation(Mutation):
    """
    Semantic mutation operator.
    
    Makes semantically meaningful changes to code while preserving structure.
    """
    
    def __init__(self, semantic_strength: float = 0.1):
        """
        Initialize semantic mutation.
        
        Args:
            semantic_strength: Strength of semantic changes
        """
        if semantic_strength < 0.0:
            raise ValueError("Semantic strength must be non-negative")
        self.semantic_strength = semantic_strength
    
    def apply(self, codes: List[Code], **kwargs) -> Code:
        """Apply semantic mutation to a single parent code."""
        if len(codes) != 1:
            raise ValueError("Semantic mutation requires exactly one parent code")
        # Implementation would be provided in concrete implementation
        raise NotImplementedError("Implementation required")
    
    def get_config(self) -> Dict[str, Any]:
        """Get semantic mutation configuration."""
        return {
            "operator": "semantic_mutation",
            "semantic_strength": self.semantic_strength
        }


def create_variation(operator: str, **kwargs) -> Variation:
    """
    Factory function for creating variation operators.
    
    Args:
        operator: Name of the variation operator
        **kwargs: Operator-specific parameters
        
    Returns:
        Variation instance
        
    Raises:
        ValueError: If operator is unknown
    """
    operators = {
        "point_mutation": PointMutation,
        "uniform_crossover": UniformCrossover,
        "semantic_mutation": SemanticMutation
    }
    
    if operator not in operators:
        available = ", ".join(operators.keys())
        raise ValueError(f"Unknown variation operator '{operator}'. Available: {available}")
    
    return operators[operator](**kwargs)
