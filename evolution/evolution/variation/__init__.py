"""
代码进化的变异操作。

本模块提供各种变异操作（突变、交叉等），用于在进化过程中创建新的代码变体。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any

from ...core.code import Code


class Variation(ABC):
    """
    变异操作的抽象基类。

    变异操作通过突变和交叉等操作修改或组合现有代码来创建新代码。
    """
    
    @abstractmethod
    def apply(self, codes: List[Code], **kwargs) -> Code:
        """
        对输入代码应用变异操作。

        参数:
            codes: 父代码列表（突变为1个，交叉为2个或更多）
            **kwargs: 额外的操作特定参数

        返回:
            通过应用变异操作创建的新代码

        抛出:
            ValueError: 如果输入代码数量对此操作无效
        """
        pass
    
    @abstractmethod
    def get_config(self) -> Dict[str, Any]:
        """
        获取此变异操作的配置参数。

        返回:
            配置参数字典
        """
        pass
    
    @abstractmethod
    def get_arity(self) -> int:
        """
        获取此操作所需的父代码数量。

        返回:
            所需父代码数量
        """
        pass


class Mutation(Variation):
    """
    突变操作的抽象基类。

    突变操作修改单个父代码以创建后代。
    """

    def get_arity(self) -> int:
        """突变需要恰好一个父代。"""
        return 1


class Crossover(Variation):
    """
    交叉操作的抽象基类。

    交叉操作组合多个父代码以创建后代。
    """

    def get_arity(self) -> int:
        """交叉需要至少两个父代。"""
        return 2


class PointMutation(Mutation):
    """
    点突变操作。

    对代码内容进行小的随机更改。
    """
    
    def __init__(self, mutation_rate: float = 0.1, mutation_strength: float = 0.1):
        """
        初始化点突变。

        参数:
            mutation_rate: 发生突变的概率
            mutation_strength: 突变的强度/幅度
        """
        if not 0.0 <= mutation_rate <= 1.0:
            raise ValueError("突变率必须在0.0到1.0之间")
        if mutation_strength < 0.0:
            raise ValueError("突变强度必须为非负数")
        
        self.mutation_rate = mutation_rate
        self.mutation_strength = mutation_strength
    
    def apply(self, codes: List[Code], **kwargs) -> Code:
        """对单个父代码应用点突变。"""
        if len(codes) != 1:
            raise ValueError("点突变需要恰好一个父代码")
        # 具体实现将在具体实现中提供
        raise NotImplementedError("需要实现")

    def get_config(self) -> Dict[str, Any]:
        """获取点突变配置。"""
        return {
            "operator": "point_mutation",
            "mutation_rate": self.mutation_rate,
            "mutation_strength": self.mutation_strength
        }


class UniformCrossover(Crossover):
    """
    均匀交叉操作。

    通过从每个父代码中随机选择元素来组合两个父代码。
    """
    
    def __init__(self, crossover_rate: float = 0.5):
        """
        初始化均匀交叉。

        参数:
            crossover_rate: 从第一个父代选择的概率
        """
        if not 0.0 <= crossover_rate <= 1.0:
            raise ValueError("交叉率必须在0.0到1.0之间")
        self.crossover_rate = crossover_rate
    
    def apply(self, codes: List[Code], **kwargs) -> Code:
        """对两个父代码应用均匀交叉。"""
        if len(codes) != 2:
            raise ValueError("均匀交叉需要恰好两个父代码")
        # 具体实现将在具体实现中提供
        raise NotImplementedError("需要实现")

    def get_config(self) -> Dict[str, Any]:
        """获取均匀交叉配置。"""
        return {
            "operator": "uniform_crossover",
            "crossover_rate": self.crossover_rate
        }


class SemanticMutation(Mutation):
    """
    语义突变操作。

    在保持结构的同时对代码进行语义上有意义的更改。
    """
    
    def __init__(self, semantic_strength: float = 0.1):
        """
        初始化语义突变。

        参数:
            semantic_strength: 语义变化的强度
        """
        if semantic_strength < 0.0:
            raise ValueError("语义强度必须为非负数")
        self.semantic_strength = semantic_strength
    
    def apply(self, codes: List[Code], **kwargs) -> Code:
        """对单个父代码应用语义突变。"""
        if len(codes) != 1:
            raise ValueError("语义突变需要恰好一个父代码")
        # 具体实现将在具体实现中提供
        raise NotImplementedError("需要实现")

    def get_config(self) -> Dict[str, Any]:
        """获取语义突变配置。"""
        return {
            "operator": "semantic_mutation",
            "semantic_strength": self.semantic_strength
        }


def create_variation(operator: str, **kwargs) -> Variation:
    """
    创建变异操作的工厂函数。

    参数:
        operator: 变异操作的名称
        **kwargs: 操作特定参数

    返回:
        变异实例

    抛出:
        ValueError: 如果操作未知
    """
    operators = {
        "point_mutation": PointMutation,
        "uniform_crossover": UniformCrossover,
        "semantic_mutation": SemanticMutation
    }
    
    if operator not in operators:
        available = ", ".join(operators.keys())
        raise ValueError(f"未知的变异操作 '{operator}'。可用操作: {available}")
    
    return operators[operator](**kwargs)
