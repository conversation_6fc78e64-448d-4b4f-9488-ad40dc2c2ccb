"""
代码表示和相关数据结构。

本模块定义了核心的Code类，用于表示进化框架中的一段代码及其相关元数据。
"""

import time
import uuid
from dataclasses import dataclass, field
from typing import Any, Dict, List


@dataclass
class Code:
    """
    表示一段代码及其元数据。

    这是框架中进化的基本单位。每个Code实例包含实际的代码内容以及评估指标、
    特征向量和谱系信息。

    属性:
        id: 此代码实例的唯一标识符
        content: 实际的代码内容字符串
        metrics: 评估指标字典（如性能、质量分数）
        features: 表示此代码特征向量的字典
        metadata: 附加元数据（如语言、框架、标签）
        timestamp: 创建此代码时的Unix时间戳
        parent_ids: 用于跟踪谱系的父代码ID列表
        generation: 进化过程中的代数
    """
    
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    content: str = ""
    metrics: Dict[str, float] = field(default_factory=dict)
    features: Dict[str, float] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)
    parent_ids: List[str] = field(default_factory=list)
    generation: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将Code实例转换为字典表示。

        返回:
            包含所有代码属性的字典
        """
        return {
            'id': self.id,
            'content': self.content,
            'metrics': self.metrics.copy(),
            'features': self.features.copy(),
            'metadata': self.metadata.copy(),
            'timestamp': self.timestamp,
            'parent_ids': self.parent_ids.copy(),
            'generation': self.generation
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Code':
        """
        从字典表示创建Code实例。

        参数:
            data: 包含代码属性的字典

        返回:
            新的Code实例
        """
        return cls(
            id=data.get('id', str(uuid.uuid4())),
            content=data.get('content', ''),
            metrics=data.get('metrics', {}).copy(),
            features=data.get('features', {}).copy(),
            metadata=data.get('metadata', {}).copy(),
            timestamp=data.get('timestamp', time.time()),
            parent_ids=data.get('parent_ids', []).copy(),
            generation=data.get('generation', 0)
        )
    
    def __eq__(self, other) -> bool:
        """基于ID检查相等性。"""
        if not isinstance(other, Code):
            return False
        return self.id == other.id

    def __hash__(self) -> int:
        """基于ID的哈希值。"""
        return hash(self.id)

    def __repr__(self) -> str:
        """用于调试的字符串表示。"""
        return f"Code(id='{self.id[:8]}...', generation={self.generation})"
