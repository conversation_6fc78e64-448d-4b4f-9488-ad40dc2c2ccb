"""
代码进化的特征空间管理。

本模块提供用于管理特征空间的类，代码根据其特征在特征空间中组织。
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple

from .code import Code


@dataclass
class FeatureSpaceConfig:
    """
    特征空间配置。

    定义特征空间的维度以及特征应如何离散化和标准化。

    属性:
        dimensions: 定义每个维度的元组列表，格式为(名称, 最小值, 最大值, 分箱数)
        normalize: 是否将特征值标准化到[0, 1]范围
    """
    
    dimensions: List[Tuple[str, float, float, int]]  # (name, min, max, bins)
    normalize: bool = True
    
    def validate(self) -> None:
        """
        验证配置。

        抛出:
            ValueError: 如果配置无效
        """
        if not self.dimensions:
            raise ValueError("At least one dimension must be specified")
        
        for name, min_val, max_val, bins in self.dimensions:
            if not isinstance(name, str) or not name:
                raise ValueError(f"Dimension name must be non-empty string")
            if min_val >= max_val:
                raise ValueError(f"Min value must be less than max value for dimension {name}")
            if bins <= 0:
                raise ValueError(f"Number of bins must be positive for dimension {name}")


class FeatureSpace(ABC):
    """
    管理代码特征空间的抽象基类。

    特征空间根据代码的特征向量组织代码，支持高效的邻居搜索和多样性维护。
    """
    
    def __init__(self, config: FeatureSpaceConfig):
        """
        初始化特征空间。

        参数:
            config: 特征空间配置
        """
        config.validate()
        self.config = config
    
    @abstractmethod
    def add(self, code: Code) -> None:
        """
        向特征空间添加代码。

        参数:
            code: 要添加的代码实例

        抛出:
            ValueError: 如果代码特征与空间不兼容
        """
        pass
    
    @abstractmethod
    def remove(self, code: Code) -> bool:
        """
        从特征空间移除代码。

        参数:
            code: 要移除的代码实例

        返回:
            如果找到并移除代码则返回True，否则返回False
        """
        pass
    
    @abstractmethod
    def get_cell(self, coordinates: Tuple[float, ...]) -> List[Code]:
        """
        获取特征空间中特定单元格的所有代码。

        参数:
            coordinates: 特征坐标

        返回:
            指定单元格中的代码列表

        抛出:
            ValueError: 如果坐标无效
        """
        pass
    
    @abstractmethod
    def get_neighbors(self, coordinates: Tuple[float, ...],
                     distance: int = 1) -> List[Code]:
        """
        获取指定距离内相邻单元格中的代码。

        参数:
            coordinates: 中心坐标
            distance: 搜索的最大距离（以单元格为单位）

        返回:
            相邻单元格中的代码列表

        抛出:
            ValueError: 如果坐标无效或距离为负数
        """
        pass
    
    @abstractmethod
    def get_stats(self) -> Dict[str, Dict[str, float]]:
        """
        获取特征空间的统计信息。

        返回:
            包含每个维度统计信息的字典，包括:
            - 'count': 代码数量
            - 'density': 每个单元格的平均代码数
            - 'coverage': 被占用单元格的比例
        """
        pass
    
    @abstractmethod
    def clear(self) -> None:
        """从特征空间移除所有代码。"""
        pass

    @abstractmethod
    def count(self) -> int:
        """
        获取特征空间中代码的总数。

        返回:
            代码总数
        """
        pass
    
    def get_dimension_names(self) -> List[str]:
        """
        获取所有维度的名称。

        返回:
            维度名称列表
        """
        return [dim[0] for dim in self.config.dimensions]
    
    def get_dimension_count(self) -> int:
        """
        获取维度数量。

        返回:
            维度数量
        """
        return len(self.config.dimensions)
