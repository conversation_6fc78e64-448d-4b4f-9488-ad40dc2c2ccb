"""
代码进化的特征空间管理。

本模块提供用于管理特征空间的类，代码根据其特征在特征空间中组织。
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple

from .code import Code


@dataclass
class FeatureSpaceConfig:
    """
    特征空间配置。

    定义特征空间的维度以及特征应如何离散化和标准化。

    属性:
        dimensions: 定义每个维度的元组列表，格式为(名称, 最小值, 最大值, 分箱数)
        normalize: 是否将特征值标准化到[0, 1]范围
    """
    
    dimensions: List[Tuple[str, float, float, int]]  # (name, min, max, bins)
    normalize: bool = True
    
    def validate(self) -> None:
        """
        验证配置。

        抛出:
            ValueError: 如果配置无效
        """
        if not self.dimensions:
            raise ValueError("At least one dimension must be specified")
        
        for name, min_val, max_val, bins in self.dimensions:
            if not isinstance(name, str) or not name:
                raise ValueError(f"Dimension name must be non-empty string")
            if min_val >= max_val:
                raise ValueError(f"Min value must be less than max value for dimension {name}")
            if bins <= 0:
                raise ValueError(f"Number of bins must be positive for dimension {name}")


class FeatureSpace(ABC):
    """
    管理代码特征空间的抽象基类。

    特征空间根据代码的特征向量组织代码，支持高效的邻居搜索和多样性维护。
    """
    
    def __init__(self, config: FeatureSpaceConfig):
        """
        初始化特征空间。

        参数:
            config: 特征空间配置
        """
        config.validate()
        self.config = config
    
    @abstractmethod
    def add(self, code: Code) -> None:
        """
        Add a code to the feature space.
        
        Args:
            code: Code instance to add
            
        Raises:
            ValueError: If code features are incompatible with the space
        """
        pass
    
    @abstractmethod
    def remove(self, code: Code) -> bool:
        """
        Remove a code from the feature space.
        
        Args:
            code: Code instance to remove
            
        Returns:
            True if code was found and removed, False otherwise
        """
        pass
    
    @abstractmethod
    def get_cell(self, coordinates: Tuple[float, ...]) -> List[Code]:
        """
        Get all codes in a specific cell of the feature space.
        
        Args:
            coordinates: Feature coordinates
            
        Returns:
            List of codes in the specified cell
            
        Raises:
            ValueError: If coordinates are invalid
        """
        pass
    
    @abstractmethod
    def get_neighbors(self, coordinates: Tuple[float, ...], 
                     distance: int = 1) -> List[Code]:
        """
        Get codes in neighboring cells within specified distance.
        
        Args:
            coordinates: Center coordinates
            distance: Maximum distance (in cells) to search
            
        Returns:
            List of codes in neighboring cells
            
        Raises:
            ValueError: If coordinates are invalid or distance is negative
        """
        pass
    
    @abstractmethod
    def get_stats(self) -> Dict[str, Dict[str, float]]:
        """
        Get statistics about the feature space.
        
        Returns:
            Dictionary with statistics for each dimension including:
            - 'count': number of codes
            - 'density': average codes per cell
            - 'coverage': fraction of cells occupied
        """
        pass
    
    @abstractmethod
    def clear(self) -> None:
        """Remove all codes from the feature space."""
        pass
    
    @abstractmethod
    def count(self) -> int:
        """
        Get total number of codes in the feature space.
        
        Returns:
            Total number of codes
        """
        pass
    
    def get_dimension_names(self) -> List[str]:
        """
        Get names of all dimensions.
        
        Returns:
            List of dimension names
        """
        return [dim[0] for dim in self.config.dimensions]
    
    def get_dimension_count(self) -> int:
        """
        Get number of dimensions.
        
        Returns:
            Number of dimensions
        """
        return len(self.config.dimensions)
