"""
进化过程控制器。

本模块提供主控制器类，通过协调所有组件来编排整个进化过程。
"""

import json
import time
from dataclasses import dataclass, asdict
from typing import Dict, Any, Optional, List

from .core.code import Code
from .organization.population import Population
from .organization.elite import Elite
from .evolution.selection import Selection
from .evolution.variation import Variation
from .evolution.evaluation import Evaluator
from .data.code_storage import CodeStorage
from .data.asset_storage import AssetStorage


@dataclass
class EvolutionConfig:
    """
    进化过程的配置。

    包含种群管理、精英保存、选择、变异和评估策略的所有配置参数。

    属性:
        population: 种群管理配置
        elite: 精英管理配置
        selection: 选择策略配置
        variation: 变异操作配置
        evaluation: 评估策略配置
    """
    
    population: Dict[str, Any]
    elite: Dict[str, Any]
    selection: Dict[str, Any]
    variation: Dict[str, Any]
    evaluation: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EvolutionConfig':
        """Create configuration from dictionary."""
        return cls(**data)
    
    def validate(self) -> None:
        """
        Validate the configuration.
        
        Raises:
            ValueError: If configuration is invalid
        """
        required_sections = ['population', 'elite', 'selection', 'variation', 'evaluation']
        for section in required_sections:
            if not hasattr(self, section) or getattr(self, section) is None:
                raise ValueError(f"Missing required configuration section: {section}")


class EvolutionController:
    """
    进化过程的主控制器。

    通过协调种群管理、精英保存、选择、变异和评估来编排整个进化过程。
    """
    
    def __init__(self, 
                 config: EvolutionConfig,
                 code_storage: CodeStorage,
                 asset_storage: Optional[AssetStorage] = None):
        """
        初始化进化控制器。

        参数:
            config: 进化配置
            code_storage: 代码存储系统
            asset_storage: 资源存储系统（可选）
        """
        config.validate()
        self.config = config
        self.code_storage = code_storage
        self.asset_storage = asset_storage
        
        # 初始化组件（在实现中将由工厂创建）
        self.population: Optional[Population] = None
        self.elite: Optional[Elite] = None
        self.selection: Optional[Selection] = None
        self.variation: Optional[Variation] = None
        self.evaluator: Optional[Evaluator] = None

        # 进化状态
        self._generation = 0
        self._total_evaluations = 0
        self._start_time = time.time()
        self._initialized = False
    
    def initialize(self, initial_code: str) -> None:
        """
        使用初始代码初始化进化过程。

        参数:
            initial_code: 开始进化的初始代码内容

        抛出:
            ValueError: 如果初始化失败
        """
        if self._initialized:
            raise ValueError("进化控制器已经初始化")

        # 创建初始代码实例
        code = Code(content=initial_code, generation=0)

        # 评估初始代码
        if self.evaluator:
            code.metrics = self.evaluator.evaluate(code)
            self._total_evaluations += 1

        # 添加到种群和精英
        if self.population:
            self.population.add(code)
        if self.elite:
            self.elite.add(code)

        # 保存到存储
        self.code_storage.save(code)
        
        self._initialized = True
    
    def step(self) -> Dict[str, Any]:
        """
        执行进化过程的一步。

        返回:
            包含步骤结果和统计信息的字典

        抛出:
            RuntimeError: 如果控制器未初始化
        """
        if not self._initialized:
            raise RuntimeError("进化控制器未初始化")

        step_start_time = time.time()
        step_stats = {
            'generation': self._generation,
            'evaluations': 0,
            'new_codes': 0,
            'elite_updates': 0,
            'step_time': 0.0
        }

        # 选择阶段
        if self.population and self.selection:
            parents = self.selection.select(
                candidates=self.population.sample(strategy="all"),
                count=self.variation.get_arity() if self.variation else 2
            )
        else:
            parents = []

        # 变异阶段
        if self.variation and parents:
            offspring = self.variation.apply(parents)
            step_stats['new_codes'] += 1

            # 评估阶段
            if self.evaluator:
                offspring.metrics = self.evaluator.evaluate(offspring)
                self._total_evaluations += 1
                step_stats['evaluations'] += 1

            # 更新代数
            offspring.generation = self._generation + 1

            # 添加到种群
            if self.population:
                self.population.add(offspring)

            # 尝试添加到精英
            if self.elite and self.elite.add(offspring):
                step_stats['elite_updates'] += 1

            # 保存到存储
            self.code_storage.save(offspring)

        # 迁移（如果适用）
        if self.population and self.population.should_migrate():
            self.population.migrate()

        # 推进代数
        self._generation += 1
        if self.population:
            self.population.advance_generation()
        
        step_stats['step_time'] = time.time() - step_start_time
        return step_stats
    
    def get_best(self, metric: str = "fitness") -> Optional[Code]:
        """
        根据特定指标获取最佳代码。

        参数:
            metric: 要优化的指标名称

        返回:
            最佳代码实例，如果没有可用代码则返回None
        """
        if self.elite:
            best_codes = self.elite.get_best(metric, count=1)
            return best_codes[0] if best_codes else None
        return None
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取全面的进化统计信息。

        返回:
            包含进化统计信息的字典
        """
        stats = {
            'generation': self._generation,
            'total_evaluations': self._total_evaluations,
            'runtime': time.time() - self._start_time,
            'initialized': self._initialized
        }
        
        # 种群统计
        if self.population:
            stats['population'] = self.population.get_stats()

        # 精英统计
        if self.elite:
            stats['elite'] = self.elite.get_stats()

        # 存储统计
        stats['storage'] = self.code_storage.get_stats()
        if self.asset_storage:
            stats['assets'] = self.asset_storage.get_stats()
        
        return stats
    
    def save_state(self, path: str) -> None:
        """
        将当前进化状态保存到文件。

        参数:
            path: 保存状态文件的路径

        抛出:
            IOError: 如果保存操作失败
        """
        state = {
            'config': self.config.to_dict(),
            'generation': self._generation,
            'total_evaluations': self._total_evaluations,
            'start_time': self._start_time,
            'initialized': self._initialized,
            'timestamp': time.time()
        }
        
        try:
            with open(path, 'w') as f:
                json.dump(state, f, indent=2)
        except Exception as e:
            raise IOError(f"保存状态失败: {e}")

    def load_state(self, path: str) -> None:
        """
        从文件加载进化状态。

        参数:
            path: 状态文件路径

        抛出:
            IOError: 如果加载操作失败
        """
        try:
            with open(path, 'r') as f:
                state = json.load(f)
            
            self._generation = state.get('generation', 0)
            self._total_evaluations = state.get('total_evaluations', 0)
            self._start_time = state.get('start_time', time.time())
            self._initialized = state.get('initialized', False)
            
        except Exception as e:
            raise IOError(f"加载状态失败: {e}")

    def reset(self) -> None:
        """将进化控制器重置为初始状态。"""
        self._generation = 0
        self._total_evaluations = 0
        self._start_time = time.time()
        self._initialized = False
        
        if self.population:
            self.population.clear()
        if self.elite:
            self.elite.clear()
        
        self.code_storage.clear()
        if self.asset_storage:
            self.asset_storage.clear()
    
    def is_initialized(self) -> bool:
        """检查控制器是否已初始化。"""
        return self._initialized

    def get_generation(self) -> int:
        """获取当前代数。"""
        return self._generation

    def get_total_evaluations(self) -> int:
        """获取已执行的评估总数。"""
        return self._total_evaluations
