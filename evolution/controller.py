"""
进化过程控制器。

本模块提供主控制器类，通过协调所有组件来编排整个进化过程。
"""

import json
import time
from dataclasses import dataclass, asdict
from typing import Dict, Any, Optional, List

from .core.code import Code
from .organization.population import Population
from .organization.elite import Elite
from .evolution.selection import Selection
from .evolution.variation import Variation
from .evolution.evaluation import Evaluator
from .data.code_storage import CodeStorage
from .data.asset_storage import AssetStorage


@dataclass
class EvolutionConfig:
    """
    进化过程的配置。

    包含种群管理、精英保存、选择、变异和评估策略的所有配置参数。

    属性:
        population: 种群管理配置
        elite: 精英管理配置
        selection: 选择策略配置
        variation: 变异操作配置
        evaluation: 评估策略配置
    """
    
    population: Dict[str, Any]
    elite: Dict[str, Any]
    selection: Dict[str, Any]
    variation: Dict[str, Any]
    evaluation: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EvolutionConfig':
        """Create configuration from dictionary."""
        return cls(**data)
    
    def validate(self) -> None:
        """
        Validate the configuration.
        
        Raises:
            ValueError: If configuration is invalid
        """
        required_sections = ['population', 'elite', 'selection', 'variation', 'evaluation']
        for section in required_sections:
            if not hasattr(self, section) or getattr(self, section) is None:
                raise ValueError(f"Missing required configuration section: {section}")


class EvolutionController:
    """
    进化过程的主控制器。

    通过协调种群管理、精英保存、选择、变异和评估来编排整个进化过程。
    """
    
    def __init__(self, 
                 config: EvolutionConfig,
                 code_storage: CodeStorage,
                 asset_storage: Optional[AssetStorage] = None):
        """
        Initialize the evolution controller.
        
        Args:
            config: Evolution configuration
            code_storage: Storage system for codes
            asset_storage: Storage system for assets (optional)
        """
        config.validate()
        self.config = config
        self.code_storage = code_storage
        self.asset_storage = asset_storage
        
        # Initialize components (would be created by factories in implementation)
        self.population: Optional[Population] = None
        self.elite: Optional[Elite] = None
        self.selection: Optional[Selection] = None
        self.variation: Optional[Variation] = None
        self.evaluator: Optional[Evaluator] = None
        
        # Evolution state
        self._generation = 0
        self._total_evaluations = 0
        self._start_time = time.time()
        self._initialized = False
    
    def initialize(self, initial_code: str) -> None:
        """
        Initialize the evolution process with initial code.
        
        Args:
            initial_code: Initial code content to start evolution
            
        Raises:
            ValueError: If initialization fails
        """
        if self._initialized:
            raise ValueError("Evolution controller already initialized")
        
        # Create initial code instance
        code = Code(content=initial_code, generation=0)
        
        # Evaluate initial code
        if self.evaluator:
            code.metrics = self.evaluator.evaluate(code)
            self._total_evaluations += 1
        
        # Add to population and elite
        if self.population:
            self.population.add(code)
        if self.elite:
            self.elite.add(code)
        
        # Save to storage
        self.code_storage.save(code)
        
        self._initialized = True
    
    def step(self) -> Dict[str, Any]:
        """
        Execute one step of the evolution process.
        
        Returns:
            Dictionary containing step results and statistics
            
        Raises:
            RuntimeError: If controller is not initialized
        """
        if not self._initialized:
            raise RuntimeError("Evolution controller not initialized")
        
        step_start_time = time.time()
        step_stats = {
            'generation': self._generation,
            'evaluations': 0,
            'new_codes': 0,
            'elite_updates': 0,
            'step_time': 0.0
        }
        
        # Selection phase
        if self.population and self.selection:
            parents = self.selection.select(
                candidates=self.population.sample(strategy="all"),
                count=self.variation.get_arity() if self.variation else 2
            )
        else:
            parents = []
        
        # Variation phase
        if self.variation and parents:
            offspring = self.variation.apply(parents)
            step_stats['new_codes'] += 1
            
            # Evaluation phase
            if self.evaluator:
                offspring.metrics = self.evaluator.evaluate(offspring)
                self._total_evaluations += 1
                step_stats['evaluations'] += 1
            
            # Update generation
            offspring.generation = self._generation + 1
            
            # Add to population
            if self.population:
                self.population.add(offspring)
            
            # Try to add to elite
            if self.elite and self.elite.add(offspring):
                step_stats['elite_updates'] += 1
            
            # Save to storage
            self.code_storage.save(offspring)
        
        # Migration (if applicable)
        if self.population and self.population.should_migrate():
            self.population.migrate()
        
        # Advance generation
        self._generation += 1
        if self.population:
            self.population.advance_generation()
        
        step_stats['step_time'] = time.time() - step_start_time
        return step_stats
    
    def get_best(self, metric: str = "fitness") -> Optional[Code]:
        """
        Get the best code according to a specific metric.
        
        Args:
            metric: Name of the metric to optimize
            
        Returns:
            Best code instance or None if no codes available
        """
        if self.elite:
            best_codes = self.elite.get_best(metric, count=1)
            return best_codes[0] if best_codes else None
        return None
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get comprehensive evolution statistics.
        
        Returns:
            Dictionary containing evolution statistics
        """
        stats = {
            'generation': self._generation,
            'total_evaluations': self._total_evaluations,
            'runtime': time.time() - self._start_time,
            'initialized': self._initialized
        }
        
        # Population stats
        if self.population:
            stats['population'] = self.population.get_stats()
        
        # Elite stats
        if self.elite:
            stats['elite'] = self.elite.get_stats()
        
        # Storage stats
        stats['storage'] = self.code_storage.get_stats()
        if self.asset_storage:
            stats['assets'] = self.asset_storage.get_stats()
        
        return stats
    
    def save_state(self, path: str) -> None:
        """
        Save the current evolution state to file.
        
        Args:
            path: Path to save state file
            
        Raises:
            IOError: If save operation fails
        """
        state = {
            'config': self.config.to_dict(),
            'generation': self._generation,
            'total_evaluations': self._total_evaluations,
            'start_time': self._start_time,
            'initialized': self._initialized,
            'timestamp': time.time()
        }
        
        try:
            with open(path, 'w') as f:
                json.dump(state, f, indent=2)
        except Exception as e:
            raise IOError(f"Failed to save state: {e}")
    
    def load_state(self, path: str) -> None:
        """
        Load evolution state from file.
        
        Args:
            path: Path to state file
            
        Raises:
            IOError: If load operation fails
        """
        try:
            with open(path, 'r') as f:
                state = json.load(f)
            
            self._generation = state.get('generation', 0)
            self._total_evaluations = state.get('total_evaluations', 0)
            self._start_time = state.get('start_time', time.time())
            self._initialized = state.get('initialized', False)
            
        except Exception as e:
            raise IOError(f"Failed to load state: {e}")
    
    def reset(self) -> None:
        """Reset the evolution controller to initial state."""
        self._generation = 0
        self._total_evaluations = 0
        self._start_time = time.time()
        self._initialized = False
        
        if self.population:
            self.population.clear()
        if self.elite:
            self.elite.clear()
        
        self.code_storage.clear()
        if self.asset_storage:
            self.asset_storage.clear()
    
    def is_initialized(self) -> bool:
        """Check if controller is initialized."""
        return self._initialized
    
    def get_generation(self) -> int:
        """Get current generation number."""
        return self._generation
    
    def get_total_evaluations(self) -> int:
        """Get total number of evaluations performed."""
        return self._total_evaluations
